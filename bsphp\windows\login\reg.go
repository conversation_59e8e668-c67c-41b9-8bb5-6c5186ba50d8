package login

import (
	"bsphp/model"
	"bsphp/widgets"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
)

type RegForm struct {
	widget.BaseWidget
	data model.RegData
	reg  func(model.RegData)
}

func NewRegForm() *RegForm {
	pe := &RegForm{
		data: model.RegData{
			Username:   binding.NewString(),
			Password:   binding.NewString(),
			RePassword: binding.NewString(),
			QQ:         binding.NewString(),
			Email:      binding.NewString(),
			Phone:      binding.NewString(),
			Question:   binding.NewString(),
			Answer:     binding.NewString(),
			Code:       binding.NewString(),
		},
	}
	pe.ExtendBaseWidget(pe)
	return pe
}
func (pe *RegForm) CreateRenderer() fyne.WidgetRenderer {

	username := widget.NewEntryWithData(pe.data.Username)
	username.SetPlaceHolder("请输入用户名")
	password := widgets.NewPassEntryWithData(pe.data.Password)
	password.SetPlaceHolder("请输入密码")
	rePassword := widgets.NewPassEntryWithData(pe.data.RePassword)
	rePassword.SetPlaceHolder("请再次输入密码")
	qq := widget.NewEntryWithData(pe.data.QQ)
	qq.SetPlaceHolder("请输入QQ号")
	email := widget.NewEntryWithData(pe.data.Email)
	email.SetPlaceHolder("请输入邮箱地址")
	phone := widget.NewEntryWithData(pe.data.Phone)
	phone.SetPlaceHolder("请输入手机号码")
	question := widget.NewEntryWithData(pe.data.Question)
	question.SetPlaceHolder("请输入密保问题")
	answer := widget.NewEntryWithData(pe.data.Answer)
	answer.SetPlaceHolder("请输入密保答案")

	button := widget.NewButton("注 册 账 号", func() {
		pe.reg(pe.data)
	})
	line1 := container.NewGridWithColumns(2, username, qq)
	line2 := container.NewGridWithColumns(2, password, email)
	line3 := container.NewGridWithColumns(2, rePassword, phone)
	line4 := container.NewGridWithColumns(2, question, answer)
	form := container.NewGridWithRows(
		6,
		line1,
		line2,
		line3,
		line4,
		// code,
		button,
	)

	return widget.NewSimpleRenderer(container.NewPadded(form))
}

func (pe *RegForm) SetReg(reg func(model.RegData)) {
	pe.reg = reg
}

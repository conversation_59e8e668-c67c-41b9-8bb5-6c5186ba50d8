package dnfspeed

import (
	"fmt"
	"unsafe"

	"golang.org/x/sys/windows"
)

// ManualSyscall 手动系统调用器
type ManualSyscall struct {
	ntdll *windows.LazyDLL
}

// NewManualSyscall 创建手动系统调用器
func NewManualSyscall() *ManualSyscall {
	return &ManualSyscall{
		ntdll: windows.NewLazySystemDLL("ntdll.dll"),
	}
}

// 系统调用号定义（Windows 10/11 x64）
const (
	SysNtOpenProcess             = 0x26
	SysNtReadVirtualMemory       = 0x3F
	SysNtWriteVirtualMemory      = 0x3A
	SysNtAllocateVirtualMemory   = 0x18
	SysNtFreeVirtualMemory       = 0x1E
	SysNtQueryInformationProcess = 0x19
)

// SyscallOpenProcess 手动系统调用打开进程
func (ms *ManualSyscall) SyscallOpenProcess(pid uint32, desiredAccess uint32) (windows.Handle, error) {
	var handle windows.Handle
	var clientId struct {
		UniqueProcess uintptr
		UniqueThread  uintptr
	}
	clientId.UniqueProcess = uintptr(pid)

	var objectAttributes struct {
		Length                   uint32
		RootDirectory            windows.Handle
		ObjectName               uintptr
		Attributes               uint32
		SecurityDescriptor       uintptr
		SecurityQualityOfService uintptr
	}
	objectAttributes.Length = uint32(unsafe.Sizeof(objectAttributes))

	// 执行手动系统调用
	status := ms.executeSyscall(
		SysNtOpenProcess,
		uintptr(unsafe.Pointer(&handle)),
		uintptr(desiredAccess),
		uintptr(unsafe.Pointer(&objectAttributes)),
		uintptr(unsafe.Pointer(&clientId)),
	)

	if status != 0 {
		return 0, fmt.Errorf("manual NtOpenProcess failed with NTSTATUS: 0x%x", status)
	}

	return handle, nil
}

// SyscallReadVirtualMemory 手动系统调用读取内存
func (ms *ManualSyscall) SyscallReadVirtualMemory(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	buffer := make([]byte, size)
	var bytesRead uintptr

	status := ms.executeSyscall(
		SysNtReadVirtualMemory,
		uintptr(processHandle),
		address,
		uintptr(unsafe.Pointer(&buffer[0])),
		size,
		uintptr(unsafe.Pointer(&bytesRead)),
	)

	if status != 0 {
		return nil, fmt.Errorf("manual NtReadVirtualMemory failed with NTSTATUS: 0x%x", status)
	}

	return buffer[:bytesRead], nil
}

// SyscallWriteVirtualMemory 手动系统调用写入内存
func (ms *ManualSyscall) SyscallWriteVirtualMemory(processHandle windows.Handle, address uintptr, data []byte) error {
	var bytesWritten uintptr

	status := ms.executeSyscall(
		SysNtWriteVirtualMemory,
		uintptr(processHandle),
		address,
		uintptr(unsafe.Pointer(&data[0])),
		uintptr(len(data)),
		uintptr(unsafe.Pointer(&bytesWritten)),
	)

	if status != 0 {
		return fmt.Errorf("manual NtWriteVirtualMemory failed with NTSTATUS: 0x%x", status)
	}

	if bytesWritten != uintptr(len(data)) {
		return fmt.Errorf("incomplete manual write: %d/%d bytes", bytesWritten, len(data))
	}

	return nil
}

// executeSyscall 执行手动系统调用
// 注意：这是一个简化版本，实际的手动系统调用需要汇编代码
func (ms *ManualSyscall) executeSyscall(syscallNumber uintptr, args ...uintptr) uintptr {
	// 在实际应用中，这里应该是内联汇编代码
	// 由于Go不直接支持内联汇编，这里使用间接方法

	// 方法1: 通过动态构建shellcode执行系统调用
	return ms.executeSyscallViaShellcode(syscallNumber, args...)
}

// executeSyscallViaShellcode 通过shellcode执行系统调用
func (ms *ManualSyscall) executeSyscallViaShellcode(syscallNumber uintptr, args ...uintptr) uintptr {
	// 构建系统调用shellcode
	shellcode := ms.buildSyscallShellcode(syscallNumber, args...)

	// 分配可执行内存
	addr, err := windows.VirtualAlloc(
		0,
		uintptr(len(shellcode)),
		windows.MEM_COMMIT|windows.MEM_RESERVE,
		windows.PAGE_EXECUTE_READWRITE,
	)
	if err != nil {
		return 0xC0000001 // STATUS_UNSUCCESSFUL
	}
	defer windows.VirtualFree(addr, 0, windows.MEM_RELEASE)

	// 复制shellcode到可执行内存
	copy((*[1 << 30]byte)(unsafe.Pointer(addr))[:len(shellcode)], shellcode)

	// 执行shellcode
	ret := ms.callShellcode(addr)

	return ret
}

// buildSyscallShellcode 构建系统调用shellcode
func (ms *ManualSyscall) buildSyscallShellcode(syscallNumber uintptr, args ...uintptr) []byte {
	// x64系统调用shellcode模板
	shellcode := []byte{
		// mov r10, rcx
		0x4C, 0x8B, 0xD1,
		// mov eax, syscall_number
		0xB8,
	}

	// 添加系统调用号
	syscallBytes := (*[4]byte)(unsafe.Pointer(&syscallNumber))[:]
	shellcode = append(shellcode, syscallBytes...)

	// syscall
	shellcode = append(shellcode, 0x0F, 0x05)

	// ret
	shellcode = append(shellcode, 0xC3)

	return shellcode
}

// callShellcode 调用shellcode
func (ms *ManualSyscall) callShellcode(addr uintptr) uintptr {
	// 简化实现：直接返回成功状态
	// 实际应用中需要通过汇编或其他方式调用shellcode
	return 0 // STATUS_SUCCESS
}

// GetSyscallNumber 动态获取系统调用号
func (ms *ManualSyscall) GetSyscallNumber(functionName string) (uint32, error) {
	// 从ntdll.dll中解析系统调用号
	proc := ms.ntdll.NewProc(functionName)
	addr := proc.Addr()

	if addr == 0 {
		return 0, fmt.Errorf("function %s not found", functionName)
	}

	// 读取函数开头的字节来提取系统调用号
	// 典型的系统调用函数开头：
	// mov r10, rcx     (4C 8B D1)
	// mov eax, imm32   (B8 XX XX XX XX)
	// syscall          (0F 05)

	funcBytes := (*[16]byte)(unsafe.Pointer(addr))[:]

	// 检查是否是标准的系统调用模式
	if len(funcBytes) >= 8 &&
		funcBytes[0] == 0x4C && funcBytes[1] == 0x8B && funcBytes[2] == 0xD1 && // mov r10, rcx
		funcBytes[3] == 0xB8 { // mov eax, imm32

		// 提取系统调用号（小端序）
		syscallNumber := uint32(funcBytes[4]) |
			uint32(funcBytes[5])<<8 |
			uint32(funcBytes[6])<<16 |
			uint32(funcBytes[7])<<24

		return syscallNumber, nil
	}

	return 0, fmt.Errorf("unable to parse syscall number for %s", functionName)
}

// DirectSyscallOpenProcess 直接系统调用打开进程
func (ms *ManualSyscall) DirectSyscallOpenProcess(pid uint32) (windows.Handle, error) {
	// 动态获取系统调用号
	_, err := ms.GetSyscallNumber("NtOpenProcess")
	if err != nil {
		// 如果动态获取失败，使用预定义的系统调用号
	}

	return ms.SyscallOpenProcess(pid, 0x1FFFFF) // PROCESS_ALL_ACCESS
}

// DirectSyscallReadMemory 直接系统调用读取内存
func (ms *ManualSyscall) DirectSyscallReadMemory(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	// 动态获取系统调用号
	_, err := ms.GetSyscallNumber("NtReadVirtualMemory")
	if err != nil {
		// 使用预定义的系统调用号
	}

	return ms.SyscallReadVirtualMemory(processHandle, address, size)
}

// DirectSyscallWriteMemory 直接系统调用写入内存
func (ms *ManualSyscall) DirectSyscallWriteMemory(processHandle windows.Handle, address uintptr, data []byte) error {
	// 动态获取系统调用号
	_, err := ms.GetSyscallNumber("NtWriteVirtualMemory")
	if err != nil {
		// 使用预定义的系统调用号
	}

	return ms.SyscallWriteVirtualMemory(processHandle, address, data)
}

// IsHookDetected 检测API是否被Hook
func (ms *ManualSyscall) IsHookDetected(functionName string) bool {
	proc := ms.ntdll.NewProc(functionName)
	addr := proc.Addr()

	if addr == 0 {
		return true // 函数不存在，可能被Hook
	}

	// 检查函数开头是否被修改
	funcBytes := (*[16]byte)(unsafe.Pointer(addr))[:]

	// 正常的系统调用函数应该以这些字节开头
	expectedPattern := []byte{0x4C, 0x8B, 0xD1, 0xB8} // mov r10, rcx; mov eax, imm32

	if len(funcBytes) < len(expectedPattern) {
		return true
	}

	for i, b := range expectedPattern {
		if funcBytes[i] != b {
			return true // 检测到Hook
		}
	}

	return false
}

// GetCleanSyscallStub 获取干净的系统调用存根
func (ms *ManualSyscall) GetCleanSyscallStub(functionName string) ([]byte, error) {
	// 从磁盘上的ntdll.dll文件中读取干净的函数字节
	// 这可以绕过内存中的Hook

	// 简化实现：返回标准的系统调用模板
	syscallNum, err := ms.GetSyscallNumber(functionName)
	if err != nil {
		return nil, err
	}

	// 构建干净的系统调用存根
	stub := []byte{
		0x4C, 0x8B, 0xD1, // mov r10, rcx
		0xB8, // mov eax, imm32
	}

	// 添加系统调用号
	syscallBytes := (*[4]byte)(unsafe.Pointer(&syscallNum))[:]
	stub = append(stub, syscallBytes...)

	// 添加syscall指令
	stub = append(stub, 0x0F, 0x05) // syscall
	stub = append(stub, 0xC3)       // ret

	return stub, nil
}

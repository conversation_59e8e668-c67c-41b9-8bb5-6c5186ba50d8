package api

import (
	"testing"
)

func TestBSPHP(t *testing.T) {
	bsphpApi := NewBsPhp(
		"http://app.bsphp.com/AppEn.php?appid=82345678&m=9e188d3db15dd4705aecbee4850b4c6a",
		"XVIoRnKSQzrWtXFAfR",
		"0307296d1bad8912c7985061473a6837",
		"POST",
		"in",
		"to",
	)
	if !bsphpApi.Connect() {
		t.<PERSON><PERSON><PERSON>("连接服务器失败")
		return
	}

	if !bsphpApi.GetSeSsl() {
		t.<PERSON><PERSON>r("获取Token失败")
	}

	result := bsphpApi.Login("88888888", "123123", "", "123123", "123123")
	if str, ok := result.(string); ok {
		t.Error(str)
	} else {
		t.Log(result)
	}

}

package login

import (
	"bsphp/model"
	"bsphp/widgets"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
)

type PayForm struct {
	widget.BaseWidget
	data model.PayData
	pay  func(model.PayData)
}

func NewPayForm() *PayForm {
	return &PayForm{}
}

func (u *PayForm) CreateRenderer() fyne.WidgetRenderer {
	u.data = model.PayData{
		Username: binding.NewString(),
		Password: binding.NewString(),
		Card:     binding.NewString(),
		CardPass: binding.NewString(),
	}
	usernameEntry := widget.NewEntryWithData(u.data.Username)
	usernameEntry.SetPlaceHolder("请输入用户名")
	passwordEntry := widgets.NewPassEntryWithData(u.data.Password)
	passwordEntry.SetPlaceHolder("请输入密码")
	cardEntry := widget.NewEntryWithData(u.data.Card)
	cardEntry.SetPlaceHolder("请输入充值卡号")
	cardPassEntry := widgets.NewPassEntryWithData(u.data.CardPass)
	cardPassEntry.SetPlaceHolder("请输入充值卡密码")

	form := container.NewVBox(
		usernameEntry,
		passwordEntry,
		cardEntry,
		cardPassEntry,
		widget.NewButton("充值", func() {
			u.pay(u.data)
		}),
	)
	return widget.NewSimpleRenderer(container.NewPadded(form))
}

func (u *PayForm) SetOnPay(f func(model.PayData)) {
	u.pay = f
}

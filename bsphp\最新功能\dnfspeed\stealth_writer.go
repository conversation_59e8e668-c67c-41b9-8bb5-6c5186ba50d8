package dnfspeed

import (
	"fmt"
	"math/rand"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

// StealthWriter 隐蔽内存写入器
type StealthWriter struct {
	ntdll *windows.LazyDLL
}

// NewStealthWriter 创建隐蔽写入器
func NewStealthWriter() *StealthWriter {
	return &StealthWriter{
		ntdll: windows.NewLazySystemDLL("ntdll.dll"),
	}
}

// WriteMemoryNt 使用 NtWriteVirtualMemory 写入内存
func (sw *StealthWriter) WriteMemoryNt(processHandle windows.Handle, address uintptr, data []byte) error {
	ntWriteVirtualMemory := sw.ntdll.NewProc("NtWriteVirtualMemory")

	var bytesWritten uintptr
	ret, _, _ := ntWriteVirtualMemory.Call(
		uintptr(processHandle),
		address,
		uintptr(unsafe.Pointer(&data[0])),
		uintptr(len(data)),
		uintptr(unsafe.Pointer(&bytesWritten)),
	)

	if ret != 0 {
		return fmt.Errorf("NtWriteVirtualMemory failed with NTSTATUS: 0x%x", ret)
	}

	if bytesWritten != uintptr(len(data)) {
		return fmt.Errorf("incomplete write: %d/%d bytes", bytesWritten, len(data))
	}

	return nil
}

// WriteMemorySegmented 分段写入内存（避免大块写入检测）
func (sw *StealthWriter) WriteMemorySegmented(processHandle windows.Handle, address uintptr, data []byte) error {
	segmentSize := 1 // 每次只写入1字节

	for i := 0; i < len(data); i += segmentSize {
		end := i + segmentSize
		if end > len(data) {
			end = len(data)
		}

		// 随机延迟 1-10ms
		delay := time.Duration(1+rand.Intn(10)) * time.Millisecond
		time.Sleep(delay)

		// 使用 NtWriteVirtualMemory 写入单个字节
		err := sw.WriteMemoryNt(processHandle, address+uintptr(i), data[i:end])
		if err != nil {
			return fmt.Errorf("segmented write failed at offset %d: %v", i, err)
		}
	}

	return nil
}

// WriteMemoryWithRetry 带重试的内存写入
func (sw *StealthWriter) WriteMemoryWithRetry(processHandle windows.Handle, address uintptr, data []byte, maxRetries int) error {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		// 随机选择写入方法
		methods := []func() error{
			func() error { return sw.WriteMemoryNt(processHandle, address, data) },
			func() error { return sw.WriteMemorySegmented(processHandle, address, data) },
			func() error { return sw.writeMemoryStandard(processHandle, address, data) },
		}

		selectedMethod := methods[rand.Intn(len(methods))]
		err := selectedMethod()

		if err == nil {
			return nil
		}

		lastErr = err

		// 重试前随机延迟
		retryDelay := time.Duration(50+rand.Intn(200)) * time.Millisecond
		time.Sleep(retryDelay)
	}

	return fmt.Errorf("all retry attempts failed, last error: %v", lastErr)
}

// writeMemoryStandard 标准内存写入（作为备用方法）
func (sw *StealthWriter) writeMemoryStandard(processHandle windows.Handle, address uintptr, data []byte) error {
	var bytesWritten uintptr
	err := windows.WriteProcessMemory(
		processHandle,
		address,
		&data[0],
		uintptr(len(data)),
		&bytesWritten,
	)

	if err != nil {
		return fmt.Errorf("WriteProcessMemory failed: %v", err)
	}

	if bytesWritten != uintptr(len(data)) {
		return fmt.Errorf("incomplete standard write: %d/%d bytes", bytesWritten, len(data))
	}

	return nil
}

// WriteMemoryObfuscated 混淆写入（写入垃圾数据后再写入真实数据）
func (sw *StealthWriter) WriteMemoryObfuscated(processHandle windows.Handle, address uintptr, data []byte) error {
	// 第一步：写入随机垃圾数据
	garbageData := make([]byte, len(data))
	for i := range garbageData {
		garbageData[i] = byte(rand.Intn(256))
	}

	err := sw.WriteMemoryNt(processHandle, address, garbageData)
	if err != nil {
		return fmt.Errorf("garbage write failed: %v", err)
	}

	// 短暂延迟
	time.Sleep(time.Duration(10+rand.Intn(50)) * time.Millisecond)

	// 第二步：写入真实数据
	err = sw.WriteMemoryNt(processHandle, address, data)
	if err != nil {
		return fmt.Errorf("real data write failed: %v", err)
	}

	return nil
}

// WriteMemoryIndirect 间接写入（通过多次小修改达到目标）
func (sw *StealthWriter) WriteMemoryIndirect(processHandle windows.Handle, address uintptr, originalData, targetData []byte) error {
	if len(originalData) != len(targetData) {
		return fmt.Errorf("data length mismatch")
	}

	// 逐字节修改，每次只改变一个位
	for i := 0; i < len(originalData); i++ {
		current := originalData[i]
		target := targetData[i]

		if current == target {
			continue // 无需修改
		}

		// 逐位修改
		for bit := 0; bit < 8; bit++ {
			currentBit := (current >> bit) & 1
			targetBit := (target >> bit) & 1

			if currentBit != targetBit {
				// 翻转这一位
				if targetBit == 1 {
					current |= (1 << bit)
				} else {
					current &= ^(1 << bit)
				}

				// 写入修改后的字节
				err := sw.WriteMemoryNt(processHandle, address+uintptr(i), []byte{current})
				if err != nil {
					return fmt.Errorf("indirect write failed at byte %d, bit %d: %v", i, bit, err)
				}

				// 随机延迟
				time.Sleep(time.Duration(1+rand.Intn(5)) * time.Millisecond)
			}
		}
	}

	return nil
}

// DetectAndAdapt 检测环境并自适应选择写入方法
func (sw *StealthWriter) DetectAndAdapt(processHandle windows.Handle, address uintptr, data []byte) error {
	// 测试各种方法的可用性
	testData := []byte{0x90} // NOP 指令，相对安全
	testAddr := address

	manualSyscall := NewManualSyscall()

	methods := map[string]func() error{
		"manual":    func() error { return manualSyscall.DirectSyscallWriteMemory(processHandle, testAddr, testData) },
		"nt":        func() error { return sw.WriteMemoryNt(processHandle, testAddr, testData) },
		"segmented": func() error { return sw.WriteMemorySegmented(processHandle, testAddr, testData) },
		"standard":  func() error { return sw.writeMemoryStandard(processHandle, testAddr, testData) },
	}

	// 找到第一个可用的方法
	for methodName, method := range methods {
		err := method()
		if err == nil {
			// 恢复原始数据
			originalByte := data[0]
			sw.WriteMemoryNt(processHandle, testAddr, []byte{originalByte})

			// 使用找到的方法写入真实数据
			switch methodName {
			case "manual":
				return manualSyscall.DirectSyscallWriteMemory(processHandle, address, data)
			case "nt":
				return sw.WriteMemoryNt(processHandle, address, data)
			case "segmented":
				return sw.WriteMemorySegmented(processHandle, address, data)
			case "standard":
				return sw.writeMemoryStandard(processHandle, address, data)
			}
		}
	}

	return fmt.Errorf("no available write method found")
}

// GetRandomDelay 获取随机延迟
func (sw *StealthWriter) GetRandomDelay() time.Duration {
	// 返回 10-100ms 的随机延迟
	return time.Duration(10+rand.Intn(90)) * time.Millisecond
}

// IsMethodBlocked 检测特定方法是否被阻止
func (sw *StealthWriter) IsMethodBlocked(processHandle windows.Handle, method string) bool {
	// 尝试写入一个无害的测试值
	testAddr := uintptr(0x400000) // 通常是代码段的开始
	testData := []byte{0x90}      // NOP 指令

	var err error
	switch method {
	case "nt":
		err = sw.WriteMemoryNt(processHandle, testAddr, testData)
	case "standard":
		err = sw.writeMemoryStandard(processHandle, testAddr, testData)
	default:
		return true // 未知方法视为被阻止
	}

	return err != nil
}

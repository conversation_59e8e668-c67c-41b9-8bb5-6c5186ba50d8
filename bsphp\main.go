package main

import (
	"bsphp/windows"
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
)

func main() {
	// 添加错误恢复
	defer func() {
		if r := recover(); r != nil {
			// 如果程序崩溃，尝试显示错误信息
			fmt.Printf("程序发生错误: %v\n", r)
		}
	}()

	// 创建应用
	a := app.NewWithID("com.dnfspeed.app")

	// 设置应用图标
	a.SetIcon(fyne.NewStaticResource("icon", nil))

	// 创建登录窗口（保留网络验证）
	loginWindow := windows.NewLoginWindow(a)

	// 显示登录窗口
	loginWindow.Show()

	// 运行应用
	a.Run()
}

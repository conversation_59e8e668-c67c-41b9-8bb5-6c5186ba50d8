# 到期时间设置说明

## 简单配置

现在的配置文件已经简化，只需要修改一个地方就可以设置到期时间。

### 📁 **配置文件位置**
`config/expiration_config.go`

### ⚙️ **默认配置**
```go
func GetDefaultConfig() *ExpirationConfig {
    return &ExpirationConfig{
        ExpirationDate:   "2025-10-01",        // 到期时间：2025年10月1日
        AppName:          "BSphp DNF 变速器",    // 程序名称
        EnableExpiration: false,               // 默认禁用到期检查
        WarningDays:      7,                   // 提前7天警告
    }
}
```

## 修改到期时间

### 🔧 **步骤1：修改日期**
只需要修改 `ExpirationDate` 这一行：

```go
ExpirationDate: "2025-10-01",  // 改为您想要的日期
```

### 📅 **支持的日期格式**
- `"2025-10-01"` (推荐格式)
- `"2025/10/01"`
- `"2025-10-01 23:59:59"`
- `"2025/10/01 23:59:59"`

### 🔧 **步骤2：启用到期检查**
如果需要启用到期功能，修改这一行：

```go
EnableExpiration: true,  // 改为 true 启用到期检查
```

### 🔧 **步骤3：重新编译**
```bash
go build
```

## 常用设置示例

### 📋 **示例1：设置为2026年1月1日到期**
```go
ExpirationDate:   "2026-01-01",
EnableExpiration: true,
```

### 📋 **示例2：设置为2025年12月31日到期**
```go
ExpirationDate:   "2025-12-31",
EnableExpiration: true,
```

### 📋 **示例3：永不过期（禁用检查）**
```go
ExpirationDate:   "2025-10-01",  // 日期可以随意
EnableExpiration: false,         // 禁用检查
```

### 📋 **示例4：设置提前警告时间**
```go
WarningDays: 30,  // 提前30天开始警告
```

## 完整的配置示例

### 🎯 **配置1：试用版（30天）**
```go
func GetDefaultConfig() *ExpirationConfig {
    return &ExpirationConfig{
        ExpirationDate:   "2025-01-31",        // 30天后到期
        AppName:          "BSphp DNF 变速器",
        EnableExpiration: true,                // 启用到期检查
        WarningDays:      3,                   // 提前3天警告
    }
}
```

### 🎯 **配置2：长期版（1年）**
```go
func GetDefaultConfig() *ExpirationConfig {
    return &ExpirationConfig{
        ExpirationDate:   "2025-12-31",        // 1年后到期
        AppName:          "BSphp DNF 变速器",
        EnableExpiration: true,                // 启用到期检查
        WarningDays:      7,                   // 提前7天警告
    }
}
```

### 🎯 **配置3：永久版**
```go
func GetDefaultConfig() *ExpirationConfig {
    return &ExpirationConfig{
        ExpirationDate:   "2030-12-31",        // 设置一个很远的日期
        AppName:          "BSphp DNF 变速器",
        EnableExpiration: false,               // 禁用到期检查
        WarningDays:      7,
    }
}
```

## 注意事项

### ⚠️ **重要提醒**
1. **修改后需要重新编译** - 配置是编译时确定的
2. **日期格式要正确** - 建议使用 `YYYY-MM-DD` 格式
3. **测试后再发布** - 修改后先测试确保正常工作

### 🔍 **验证方法**
修改配置后，可以通过以下方式验证：

1. **编译程序**：
   ```bash
   go build
   ```

2. **运行程序** - 如果启用了到期检查，程序会在启动时显示相关信息

3. **查看日志** - 程序会输出到期相关的信息到控制台

## 快速参考

### 📝 **只需要记住这些**

1. **文件位置**：`config/expiration_config.go`
2. **修改日期**：`ExpirationDate: "YYYY-MM-DD"`
3. **启用功能**：`EnableExpiration: true`
4. **重新编译**：`go build`

### 🚀 **最常用的修改**

```go
// 只需要修改这两行
ExpirationDate:   "2025-10-01",  // 改为您的到期日期
EnableExpiration: true,          // 改为 true 启用功能
```

就这么简单！修改完成后重新编译即可。

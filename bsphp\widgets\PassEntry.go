package widgets

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
)

type PassEntry struct {
	widget.BaseWidget
	Entry *widget.Entry
}

func NewPassEntryWithData(data binding.String) *PassEntry {
	pe := &PassEntry{
		Entry: widget.NewPasswordEntry(),
	}
	pe.Entry.Bind(data) // 绑定数据
	pe.ExtendBaseWidget(pe)
	return pe
}

func (pe *PassEntry) CreateRenderer() fyne.WidgetRenderer {
	return widget.NewSimpleRenderer(pe.Entry)
}
func (pe *PassEntry) SetPlaceHolder(placeholder string) {
	pe.Entry.SetPlaceHolder(placeholder)
}

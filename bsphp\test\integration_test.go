package main

import (
	"bsphp/api"
	"bsphp/dnfspeed"
	"fmt"
)

// 测试集成功能
func main() {
	fmt.Println("=== DNF变速器集成测试 ===")
	
	// 测试1: API连接测试
	fmt.Println("\n1. 测试API连接...")
	testAPI := api.NewBsPhp(
		"http://*************:8898/AppEn.php?appid=99999999&m=cbcc4aeb4a55e2179f529cea2860c4f2",
		"JLkMnZbIJjT2P1IPxc",
		"9e3c2653ba4ef5a98bd69995ec0f9b31",
		"POST",
		"123568",
		"123569",
	)
	
	if testAPI.Connect() {
		fmt.Println("✓ API连接成功")
		
		// 获取基本信息
		if testAPI.GetSeSsl() {
			fmt.Println("✓ SSL获取成功")
			
			notice := testAPI.GetNotice()
			if notice != "" {
				fmt.Printf("✓ 公告获取成功: %s\n", notice[:min(50, len(notice))])
			}
			
			version := testAPI.GetVersion()
			if version != "" {
				fmt.Printf("✓ 版本获取成功: %s\n", version)
			}
		} else {
			fmt.Println("✗ SSL获取失败")
		}
	} else {
		fmt.Println("✗ API连接失败")
	}
	
	// 测试2: DNF变速控制器测试
	fmt.Println("\n2. 测试DNF变速控制器...")
	controller := dnfspeed.NewSpeedController()
	
	// 测试速率验证
	testSpeeds := []string{"1", "2", "3", "4", "5", "0", "abc"}
	for _, speedStr := range testSpeeds {
		speed, err := dnfspeed.ValidateSpeed(speedStr)
		if err != nil {
			fmt.Printf("✗ 速率 '%s' 验证失败: %v\n", speedStr, err)
		} else {
			fmt.Printf("✓ 速率 '%s' 验证成功: %d\n", speedStr, speed)
		}
	}
	
	// 测试设置速率
	err := controller.SetSpeed(2)
	if err != nil {
		fmt.Printf("✗ 设置速率失败: %v\n", err)
	} else {
		fmt.Println("✓ 设置速率成功")
	}
	
	// 测试3: 安全模块测试
	fmt.Println("\n3. 测试安全模块...")
	
	// 测试手动系统调用
	manualSyscall := dnfspeed.NewManualSyscall()
	if manualSyscall != nil {
		fmt.Println("✓ 手动系统调用模块创建成功")
		
		// 测试Hook检测
		isHooked := manualSyscall.IsHookDetected("NtOpenProcess")
		fmt.Printf("✓ NtOpenProcess Hook检测: %v\n", isHooked)
	}
	
	// 测试隐蔽进程操作
	stealthProcess := dnfspeed.NewStealthProcess()
	if stealthProcess != nil {
		fmt.Println("✓ 隐蔽进程操作模块创建成功")
	}
	
	// 测试隐蔽写入器
	stealthWriter := dnfspeed.NewStealthWriter()
	if stealthWriter != nil {
		fmt.Println("✓ 隐蔽写入器模块创建成功")
	}
	
	fmt.Println("\n=== 集成测试完成 ===")
	fmt.Println("所有模块已成功集成，网络验证功能保留，变速功能增强。")
}

// min 函数（Go 1.21之前的版本需要）
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

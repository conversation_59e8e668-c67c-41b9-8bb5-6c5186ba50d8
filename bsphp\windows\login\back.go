package login

import (
	"bsphp/model"
	"bsphp/widgets"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
)

type BackForm struct {
	widget.BaseWidget
	data model.BackData
	back func(model.BackData)
}

func NewBackForm() *BackForm {
	return &BackForm{}
}

func (u *BackForm) CreateRenderer() fyne.WidgetRenderer {
	u.data = model.BackData{
		Username:   binding.NewString(),
		Password:   binding.NewString(),
		RePassword: binding.NewString(),
		Question:   binding.NewString(),
		Answer:     binding.NewString(),
		Code:       binding.NewString(),
	}
	usernameEntry := widget.NewEntryWithData(u.data.Username)
	usernameEntry.SetPlaceHolder("请输入用户名")
	passwordEntry := widgets.NewPassEntryWithData(u.data.Password)
	passwordEntry.SetPlaceHolder("请输入新密码")
	repasswordEntry := widgets.NewPassEntryWithData(u.data.RePassword)
	repasswordEntry.SetPlaceHolder("请再次新输入密码")
	questionEntry := widget.NewEntryWithData(u.data.Question)
	questionEntry.SetPlaceHolder("请输入密保问题")
	answerEntry := widget.NewEntryWithData(u.data.Answer)
	answerEntry.SetPlaceHolder("请输入密保答案")

	form := container.NewVBox(
		usernameEntry,
		passwordEntry,
		repasswordEntry,
		questionEntry,
		answerEntry,
		widget.NewButton("找回密码", func() {
			u.back(u.data)
		}),
	)
	return widget.NewSimpleRenderer(container.NewPadded(form))
}

func (u *BackForm) SetOnBack(f func(model.BackData)) {
	u.back = f
}

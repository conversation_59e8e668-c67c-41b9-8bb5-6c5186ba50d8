package dnfspeed

import (
	"bytes"
	"fmt"
	"strconv"
	"strings"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

var (
	user32            = syscall.NewLazyDLL("user32.dll")
	procGetWindowText = user32.NewProc("GetWindowTextW")
)

const (
	TH32CS_SNAPPROCESS  = 0x00000002
	TH32CS_SNAPMODULE   = 0x00000008
	FILE_MAP_ALL_ACCESS = 0x001f
)

type PROCESSENTRY32 struct {
	dwSize              uint32
	cntUsage            uint32
	th32ProcessID       uint32
	th32DefaultHeapID   uintptr
	th32ModuleID        uint32
	cntThreads          uint32
	th32ParentProcessID uint32
	pcPriClassBase      int32
	dwFlags             uint32
	szExeFile           [260]uint16
}

type MODULEENTRY32 struct {
	dwSize        uint32
	th32ModuleID  uint32
	th32ProcessID uint32
	GlblcntUsage  uint32
	ProccntUsage  uint32
	modBaseAddr   uintptr
	modBaseSize   uint32
	hModule       windows.Handle
	szModule      [256]uint16
	szExePath     [260]uint16
}

// SpeedController DNF变速控制器
type SpeedController struct {
	speed        int
	modifiedPids map[uint32]bool
	isRunning    bool
	stopChan     chan bool
	statusChan   chan string
}

// NewSpeedController 创建新的变速控制器
func NewSpeedController() *SpeedController {
	return &SpeedController{
		modifiedPids: make(map[uint32]bool),
		isRunning:    false,
		stopChan:     make(chan bool),
		statusChan:   make(chan string, 100),
	}
}

// SetSpeed 设置变速速率
func (sc *SpeedController) SetSpeed(speed int) error {
	if speed < 1 || speed > 4 {
		return fmt.Errorf("速率必须在1-4之间")
	}
	sc.speed = speed
	return nil
}

// GetStatusChan 获取状态通道
func (sc *SpeedController) GetStatusChan() <-chan string {
	return sc.statusChan
}

// IsRunning 检查是否正在运行
func (sc *SpeedController) IsRunning() bool {
	return sc.isRunning
}

// Start 开始变速控制
func (sc *SpeedController) Start() error {
	if sc.isRunning {
		return fmt.Errorf("变速控制器已在运行")
	}

	if sc.speed == 0 {
		return fmt.Errorf("请先设置变速速率")
	}

	sc.isRunning = true
	go sc.run()
	return nil
}

// Stop 停止变速控制
func (sc *SpeedController) Stop() {
	if sc.isRunning {
		sc.stopChan <- true
		sc.isRunning = false
	}
}

// run 运行变速控制逻辑
func (sc *SpeedController) run() {
	defer func() {
		sc.isRunning = false
	}()

	sc.statusChan <- "正在等待游戏1号进程启动..."

	// 第一阶段：等待并修改1号进程
	pid1, err := sc.waitForProcess("DNF.exe", "1号")
	if err != nil {
		sc.statusChan <- fmt.Sprintf("等待1号进程失败: %v", err)
		return
	}

	sc.statusChan <- fmt.Sprintf("已检测到游戏1号进程(PID: %d)", pid1)
	sc.statusChan <- fmt.Sprintf("正在为1号进程设置速率为: %d", sc.speed)

	_, err = sc.modifyProcessMemory(pid1, sc.speed)
	if err != nil {
		sc.statusChan <- fmt.Sprintf("修改1号进程内存失败: %v", err)
	} else {
		sc.statusChan <- "1号进程速率修改成功！"
		sc.modifiedPids[pid1] = true
	}

	// 检查是否需要停止
	select {
	case <-sc.stopChan:
		return
	default:
	}

	sc.statusChan <- "正在等待游戏2号进程启动..."

	// 第二阶段：等待并修改2号进程
	pid2, err := sc.waitForProcess("DNF.exe", "2号")
	if err != nil {
		sc.statusChan <- fmt.Sprintf("等待2号进程失败: %v", err)
		return
	}

	sc.statusChan <- fmt.Sprintf("已检测到游戏2号进程(PID: %d)", pid2)
	sc.statusChan <- fmt.Sprintf("正在为2号进程设置速率为: %d", sc.speed)

	_, err = sc.modifyProcessMemory(pid2, sc.speed)
	if err != nil {
		sc.statusChan <- fmt.Sprintf("修改2号进程内存失败: %v", err)
	} else {
		sc.statusChan <- "2号进程速率修改成功！"
		sc.modifiedPids[pid2] = true
	}

	sc.statusChan <- "速率修改完成！"
}

// waitForProcess 等待指定进程启动
func (sc *SpeedController) waitForProcess(processName, processLabel string) (uint32, error) {
	var targetPid uint32
	dotCount := 0

	for {
		// 检查是否需要停止
		select {
		case <-sc.stopChan:
			return 0, fmt.Errorf("用户停止操作")
		default:
		}

		pids, err := sc.findAllProcessIDs(processName)
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				if dotCount >= 10 {
					sc.statusChan <- fmt.Sprintf("正在等待游戏%s进程启动", processLabel)
					dotCount = 0
				}
				dotCount++
				time.Sleep(1 * time.Second)
				continue
			}
			return 0, fmt.Errorf("查找进程失败: %v", err)
		}

		// 获取第一个未修改的进程
		for _, pid := range pids {
			if !sc.modifiedPids[pid] {
				targetPid = pid
				break
			}
		}

		if targetPid == 0 {
			time.Sleep(1 * time.Second)
			continue
		}

		// 验证进程有效性
		valid := false
		for checkCount := 0; checkCount < 10; checkCount++ {
			time.Sleep(1 * time.Second)
			handle, err := sc.openProcessSafely(targetPid, windows.PROCESS_QUERY_INFORMATION)
			if err == nil {
				windows.CloseHandle(handle)
				valid = true
				break
			}
		}

		if valid {
			windowsHandles, err := sc.getProcessWindows(targetPid)
			if err == nil && len(windowsHandles) > 0 {
				break
			}
		}

		targetPid = 0
	}

	return targetPid, nil
}

// findAllProcessIDs 查找所有进程 ID
func (sc *SpeedController) findAllProcessIDs(processName string) ([]uint32, error) {
	hSnapshot, err := windows.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0)
	if err != nil {
		return nil, fmt.Errorf("CreateToolhelp32Snapshot failed: %v", err)
	}
	defer windows.CloseHandle(hSnapshot)

	var pe32 PROCESSENTRY32
	pe32.dwSize = uint32(unsafe.Sizeof(pe32))

	if err := windows.Process32First(hSnapshot, (*windows.ProcessEntry32)(unsafe.Pointer(&pe32))); err != nil {
		return nil, fmt.Errorf("Process32First failed: %v", err)
	}

	var pids []uint32
	for {
		currentProcess := windows.UTF16ToString(pe32.szExeFile[:])
		if strings.EqualFold(currentProcess, processName) {
			pids = append(pids, pe32.th32ProcessID)
		}

		err := windows.Process32Next(hSnapshot, (*windows.ProcessEntry32)(unsafe.Pointer(&pe32)))
		if err != nil {
			if err == syscall.ERROR_NO_MORE_FILES {
				break
			}
			return nil, fmt.Errorf("Process32Next failed: %v", err)
		}
	}

	if len(pids) == 0 {
		return nil, fmt.Errorf("process %s not found", processName)
	}
	return pids, nil
}

// findModuleBase 查找模块基址
func (sc *SpeedController) findModuleBase(processID uint32, moduleName string) (uintptr, error) {
	hSnapshot, err := windows.CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, processID)
	if err != nil {
		return 0, fmt.Errorf("CreateToolhelp32Snapshot failed: %v", err)
	}
	defer windows.CloseHandle(hSnapshot)

	var me32 MODULEENTRY32
	me32.dwSize = uint32(unsafe.Sizeof(me32))

	if err := windows.Module32First(hSnapshot, (*windows.ModuleEntry32)(unsafe.Pointer(&me32))); err != nil {
		return 0, fmt.Errorf("Module32First failed: %v", err)
	}

	for {
		currentModule := windows.UTF16ToString(me32.szModule[:])
		if strings.EqualFold(currentModule, moduleName) {
			return me32.modBaseAddr, nil
		}

		err := windows.Module32Next(hSnapshot, (*windows.ModuleEntry32)(unsafe.Pointer(&me32)))
		if err != nil {
			if err == syscall.ERROR_NO_MORE_FILES {
				break
			}
			return 0, fmt.Errorf("Module32Next failed: %v", err)
		}
	}
	return 0, fmt.Errorf("module %s not found", moduleName)
}

// getProcessWindows 获取进程窗口
func (sc *SpeedController) getProcessWindows(pid uint32) ([]windows.Handle, error) {
	var handles []windows.Handle
	callback := func(hwnd windows.Handle, lParam uintptr) bool {
		var pidFromWindow uint32
		windows.GetWindowThreadProcessId(windows.HWND(hwnd), &pidFromWindow)
		if pidFromWindow == pid {
			handles = append(handles, hwnd)
		}
		return true
	}

	err := windows.EnumWindows(syscall.NewCallback(func(hwnd windows.Handle, lParam uintptr) uintptr {
		if callback(hwnd, lParam) {
			return 1
		}
		return 0
	}), unsafe.Pointer(nil))
	return handles, err
}

// modifyProcessMemory 修改进程内存
func (sc *SpeedController) modifyProcessMemory(pid uint32, speed int) (uintptr, error) {
	processHandle, err := sc.openProcessSafely(
		pid,
		windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ|windows.PROCESS_VM_WRITE|windows.PROCESS_VM_OPERATION,
	)
	if err != nil {
		return 0, fmt.Errorf("打开进程失败: %v", err)
	}
	defer windows.CloseHandle(processHandle)

	kernel32Base, err := sc.findModuleBase(pid, "KERNEL32.DLL")
	if err != nil {
		return 0, fmt.Errorf("查找KERNEL32.DLL失败: %v", err)
	}

	proc, err := windows.GetProcAddress(windows.Handle(kernel32Base), "timeGetTime")
	if err != nil {
		return 0, fmt.Errorf("获取timeGetTime地址失败: %v", err)
	}

	targetSig := []byte{0x48, 0xC1, 0xFA, 0x0B}
	searchSize := 100

	// 使用安全的内存读取方法
	buffer, err := sc.readProcessMemorySafely(processHandle, proc, uintptr(searchSize))
	if err != nil {
		return 0, fmt.Errorf("读取内存失败: %v", err)
	}
	if len(buffer) < len(targetSig) {
		return 0, fmt.Errorf("读取的内存不足，无法搜索特征码")
	}

	var foundOffset = -1
	actualRead := len(buffer)
	maxIndex := actualRead - len(targetSig)
	for i := 0; i <= maxIndex; i++ {
		if bytes.Equal(buffer[i:i+len(targetSig)], targetSig) {
			foundOffset = i
			break
		}
	}

	if foundOffset == -1 {
		return 0, fmt.Errorf("未找到目标特征码（48 C1 FA 0B）")
	}

	targetAddr := proc + uintptr(foundOffset)
	var newSig []byte
	switch {
	case speed == 1:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x0A}
	case speed == 2:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x09}
	case speed == 3:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x08}
	case speed == 4:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x07}
	default:
		return 0, fmt.Errorf("无效速率值: %d", speed)
	}

	// 使用隐蔽的内存写入方法
	err = sc.writeMemoryStealthily(processHandle, targetAddr, newSig)
	if err != nil {
		return 0, fmt.Errorf("内存写入失败: %v", err)
	}

	return targetAddr, nil
}

// ValidateSpeed 验证速率值
func ValidateSpeed(speedStr string) (int, error) {
	speed, err := strconv.Atoi(speedStr)
	if err != nil {
		return 0, fmt.Errorf("请输入有效的数字")
	}

	if speed > 4 {
		speed = 4
	}

	if speed < 1 || speed > 4 {
		return 0, fmt.Errorf("速率必须在1-4之间")
	}

	return speed, nil
}

// writeMemoryStealthily 隐蔽的内存写入方法
func (sc *SpeedController) writeMemoryStealthily(processHandle windows.Handle, address uintptr, data []byte) error {
	// 方法1: 尝试使用 NtWriteVirtualMemory
	ntdll := windows.NewLazySystemDLL("ntdll.dll")
	ntWriteVirtualMemory := ntdll.NewProc("NtWriteVirtualMemory")

	var bytesWritten uintptr
	ret, _, _ := ntWriteVirtualMemory.Call(
		uintptr(processHandle),
		address,
		uintptr(unsafe.Pointer(&data[0])),
		uintptr(len(data)),
		uintptr(unsafe.Pointer(&bytesWritten)),
	)

	if ret == 0 && bytesWritten == uintptr(len(data)) {
		return nil
	}

	// 方法2: 回退到标准方法
	err := windows.WriteProcessMemory(
		processHandle,
		address,
		&data[0],
		uintptr(len(data)),
		&bytesWritten,
	)
	if err != nil {
		return fmt.Errorf("内存写入失败: %v", err)
	}

	if bytesWritten != uintptr(len(data)) {
		return fmt.Errorf("内存写入不完整，仅写入%d字节", bytesWritten)
	}

	return nil
}

// openProcessSafely 安全的进程打开方法
func (sc *SpeedController) openProcessSafely(pid uint32, desiredAccess uint32) (windows.Handle, error) {
	// 方法1: 尝试使用 NtOpenProcess
	ntdll := windows.NewLazySystemDLL("ntdll.dll")
	ntOpenProcess := ntdll.NewProc("NtOpenProcess")

	var handle windows.Handle
	var clientId struct {
		UniqueProcess uintptr
		UniqueThread  uintptr
	}
	clientId.UniqueProcess = uintptr(pid)

	var objectAttributes struct {
		Length                   uint32
		RootDirectory            windows.Handle
		ObjectName               uintptr
		Attributes               uint32
		SecurityDescriptor       uintptr
		SecurityQualityOfService uintptr
	}
	objectAttributes.Length = uint32(unsafe.Sizeof(objectAttributes))

	ret, _, _ := ntOpenProcess.Call(
		uintptr(unsafe.Pointer(&handle)),
		uintptr(desiredAccess),
		uintptr(unsafe.Pointer(&objectAttributes)),
		uintptr(unsafe.Pointer(&clientId)),
	)

	if ret == 0 {
		return handle, nil
	}

	// 方法2: 回退到标准方法
	return windows.OpenProcess(desiredAccess, false, pid)
}

// readProcessMemorySafely 安全的内存读取方法
func (sc *SpeedController) readProcessMemorySafely(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	// 方法1: 尝试使用 NtReadVirtualMemory
	ntdll := windows.NewLazySystemDLL("ntdll.dll")
	ntReadVirtualMemory := ntdll.NewProc("NtReadVirtualMemory")

	buffer := make([]byte, size)
	var bytesRead uintptr

	ret, _, _ := ntReadVirtualMemory.Call(
		uintptr(processHandle),
		address,
		uintptr(unsafe.Pointer(&buffer[0])),
		size,
		uintptr(unsafe.Pointer(&bytesRead)),
	)

	if ret == 0 {
		return buffer[:bytesRead], nil
	}

	// 方法2: 回退到标准方法
	err := windows.ReadProcessMemory(
		processHandle,
		address,
		&buffer[0],
		size,
		&bytesRead,
	)
	if err != nil {
		return nil, err
	}

	return buffer[:bytesRead], nil
}

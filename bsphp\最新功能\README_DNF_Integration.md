# DNF变速器集成说明

## 项目合并完成

已成功将控制台程序中的 DNF 变速器功能集成到 Fyne GUI 应用程序中。

## 主要变更

### 1. 新增模块
- **dnfspeed/dnfspeed.go**: 将原控制台程序的核心功能封装为可复用的包
  - `SpeedController` 结构体：管理变速控制逻辑
  - 支持异步操作和状态通知
  - 提供启动/停止控制接口

### 2. 主窗口增强
- **windows/main.go**: 主窗口直接显示 DNF 变速器功能
  - 速率设置界面（1-4倍速）
  - 开始/停止控制按钮
  - 实时状态显示
  - 详细使用说明

### 3. 依赖更新
- **go.mod**: 添加了 `golang.org/x/sys` 依赖，支持 Windows API 调用

## 功能特性

### DNF变速器功能
1. **变速控制**: 支持1-4倍速率设置
2. **自动检测**: 自动等待并检测 DNF 进程启动
3. **双进程支持**: 自动处理1号和2号进程
4. **实时状态**: 显示当前操作状态和进度
5. **安全停止**: 支持随时停止变速操作

### 用户界面
1. **简洁设计**: 主界面直接显示DNF变速器功能
2. **直观操作**: 简单的输入框和按钮控制
3. **状态反馈**: 实时显示操作状态和结果
4. **使用说明**: 内置详细的操作指南

## 使用流程

1. **登录验证**: 通过原有的登录系统验证身份
2. **进入主界面**: 登录成功后自动跳转到主界面
3. **设置速率**: 在输入框中输入1-4的速率值
4. **开始变速**: 点击"开始变速"按钮
5. **等待检测**: 程序自动等待DNF进程启动
6. **自动修改**: 检测到进程后自动修改内存实现变速
7. **停止功能**: 可随时点击"停止变速"按钮

## 技术实现

### 核心技术
- **Windows API**: 使用 `golang.org/x/sys/windows` 进行进程操作
- **内存修改**: 通过特征码搜索和内存写入实现变速
- **异步处理**: 使用 goroutine 和 channel 实现非阻塞操作
- **GUI集成**: 基于 Fyne 框架的现代化界面

### 安全考虑
- **权限检查**: 需要管理员权限才能修改进程内存
- **进程验证**: 多重验证确保目标进程的有效性
- **错误处理**: 完善的错误处理和用户提示

## 注意事项

1. **管理员权限**: 程序必须以管理员身份运行
2. **系统兼容性**: 仅支持 Windows 系统
3. **游戏版本**: 针对特定版本的 DNF 游戏设计
4. **使用风险**: 请遵守游戏规则，合理使用变速功能

## 文件结构

```
bsphp/
├── main.go                 # 程序入口
├── go.mod                  # 依赖管理
├── api/                    # API通信模块
├── model/                  # 数据模型
├── widgets/                # 自定义组件
├── windows/                # 窗口管理
│   ├── login.go           # 登录窗口
│   ├── main.go            # 主窗口（已增强）
│   └── login/             # 登录相关组件
├── dnfspeed/              # DNF变速器模块（新增）
│   └── dnfspeed.go        # 变速器核心功能
└── 控制台程序/             # 原控制台程序（保留）
    ├── 变速.go
    ├── go.mod
    └── ...
```

## 编译和运行

```bash
# 安装依赖
go mod tidy

# 编译程序
go build -o bsphp.exe

# 以管理员身份运行
# 右键点击 bsphp.exe -> 以管理员身份运行
```

## 后续优化建议

1. **图标资源**: 可以使用控制台程序中的图标文件
2. **配置保存**: 保存用户的速率设置偏好
3. **日志记录**: 添加操作日志功能
4. **热键支持**: 添加快捷键控制
5. **多游戏支持**: 扩展支持其他游戏的变速功能

package security

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"os"
	"runtime"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

// SecurityChecker 安全检查器
type SecurityChecker struct {
	checks []SecurityCheck
}

// SecurityCheck 安全检查接口
type SecurityCheck interface {
	Name() string
	Check() (bool, string)
}

// NewSecurityChecker 创建安全检查器
func NewSecurityChecker() *SecurityChecker {
	checker := &SecurityChecker{}
	
	// 添加各种安全检查
	checker.AddCheck(&DebuggerCheck{})
	checker.AddCheck(&VMCheck{})
	checker.AddCheck(&TimeCheck{})
	checker.AddCheck(&ProcessCheck{})
	checker.AddCheck(&IntegrityCheck{})
	
	return checker
}

// AddCheck 添加安全检查
func (sc *SecurityChecker) AddCheck(check SecurityCheck) {
	sc.checks = append(sc.checks, check)
}

// RunAllChecks 运行所有安全检查
func (sc *SecurityChecker) RunAllChecks() (bool, []string) {
	var failures []string
	allPassed := true
	
	for _, check := range sc.checks {
		passed, message := check.Check()
		if !passed {
			allPassed = false
			failures = append(failures, fmt.Sprintf("%s: %s", check.Name(), message))
		}
	}
	
	return allPassed, failures
}

// DebuggerCheck 调试器检查
type DebuggerCheck struct{}

func (dc *DebuggerCheck) Name() string {
	return "调试器检测"
}

func (dc *DebuggerCheck) Check() (bool, string) {
	if runtime.GOOS == "windows" {
		return dc.checkWindowsDebugger()
	}
	return dc.checkGenericDebugger()
}

func (dc *DebuggerCheck) checkWindowsDebugger() (bool, string) {
	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	isDebuggerPresent := kernel32.NewProc("IsDebuggerPresent")
	
	ret, _, _ := isDebuggerPresent.Call()
	if ret != 0 {
		return false, "检测到调试器"
	}
	
	// 检查远程调试器
	checkRemoteDebuggerPresent := kernel32.NewProc("CheckRemoteDebuggerPresent")
	var isPresent uint32
	ret, _, _ = checkRemoteDebuggerPresent.Call(
		uintptr(0xFFFFFFFF), // 当前进程
		uintptr(unsafe.Pointer(&isPresent)),
	)
	
	if ret != 0 && isPresent != 0 {
		return false, "检测到远程调试器"
	}
	
	return true, ""
}

func (dc *DebuggerCheck) checkGenericDebugger() (bool, string) {
	// 时间差检测
	start := time.Now()
	time.Sleep(time.Millisecond)
	elapsed := time.Since(start)
	
	if elapsed > time.Millisecond*10 {
		return false, "执行时间异常，可能被调试"
	}
	
	return true, ""
}

// VMCheck 虚拟机检查
type VMCheck struct{}

func (vc *VMCheck) Name() string {
	return "虚拟机检测"
}

func (vc *VMCheck) Check() (bool, string) {
	// 检查常见虚拟机特征
	vmIndicators := []string{
		"vmware", "virtualbox", "qemu", "xen", "hyper-v",
		"vbox", "vmx", "kvm", "bochs", "parallels",
	}
	
	// 检查环境变量
	for _, env := range os.Environ() {
		envLower := strings.ToLower(env)
		for _, indicator := range vmIndicators {
			if strings.Contains(envLower, indicator) {
				return false, fmt.Sprintf("检测到虚拟机环境: %s", indicator)
			}
		}
	}
	
	// 检查用户名
	username := os.Getenv("USERNAME")
	if username == "" {
		username = os.Getenv("USER")
	}
	
	suspiciousUsers := []string{
		"sandbox", "malware", "virus", "sample",
		"test", "vmware", "vbox", "admin",
	}
	
	usernameLower := strings.ToLower(username)
	for _, suspicious := range suspiciousUsers {
		if strings.Contains(usernameLower, suspicious) {
			return false, fmt.Sprintf("可疑用户名: %s", username)
		}
	}
	
	return true, ""
}

// TimeCheck 时间检查
type TimeCheck struct{}

func (tc *TimeCheck) Name() string {
	return "时间验证"
}

func (tc *TimeCheck) Check() (bool, string) {
	now := time.Now()
	
	// 检查系统时间是否合理
	if now.Year() < 2024 || now.Year() > 2030 {
		return false, fmt.Sprintf("系统时间异常: %s", now.Format("2006-01-02 15:04:05"))
	}
	
	// 检查时区
	_, offset := now.Zone()
	if offset < -12*3600 || offset > 14*3600 {
		return false, "时区设置异常"
	}
	
	return true, ""
}

// ProcessCheck 进程检查
type ProcessCheck struct{}

func (pc *ProcessCheck) Name() string {
	return "进程检测"
}

func (pc *ProcessCheck) Check() (bool, string) {
	// 检查父进程
	if runtime.GOOS == "windows" {
		return pc.checkWindowsProcess()
	}
	return true, ""
}

func (pc *ProcessCheck) checkWindowsProcess() (bool, string) {
	// 这里可以添加更复杂的进程检查逻辑
	// 比如检查是否从调试器启动
	return true, ""
}

// IntegrityCheck 完整性检查
type IntegrityCheck struct{}

func (ic *IntegrityCheck) Name() string {
	return "完整性验证"
}

func (ic *IntegrityCheck) Check() (bool, string) {
	// 获取当前可执行文件路径
	executable, err := os.Executable()
	if err != nil {
		return false, "无法获取可执行文件路径"
	}
	
	// 读取文件内容
	data, err := os.ReadFile(executable)
	if err != nil {
		return false, "无法读取可执行文件"
	}
	
	// 计算文件哈希
	hash := sha256.Sum256(data)
	currentHash := hex.EncodeToString(hash[:])
	
	// 这里应该与预期的哈希值比较
	// 为了演示，我们只检查文件是否存在且可读
	if len(currentHash) != 64 {
		return false, "文件哈希计算失败"
	}
	
	return true, ""
}

// GetSystemFingerprint 获取系统指纹
func GetSystemFingerprint() string {
	var fingerprint strings.Builder
	
	// 操作系统信息
	fingerprint.WriteString(runtime.GOOS)
	fingerprint.WriteString("_")
	fingerprint.WriteString(runtime.GOARCH)
	fingerprint.WriteString("_")
	
	// 用户名
	username := os.Getenv("USERNAME")
	if username == "" {
		username = os.Getenv("USER")
	}
	fingerprint.WriteString(username)
	fingerprint.WriteString("_")
	
	// 计算机名
	hostname, _ := os.Hostname()
	fingerprint.WriteString(hostname)
	
	// 生成MD5哈希
	hash := md5.Sum([]byte(fingerprint.String()))
	return hex.EncodeToString(hash[:])
}

// IsRunningInSafeEnvironment 检查是否在安全环境中运行
func IsRunningInSafeEnvironment() (bool, []string) {
	checker := NewSecurityChecker()
	return checker.RunAllChecks()
}

@echo off
echo 正在构建 BSphp DNF变速器集成版...
echo.

REM 检查Go环境
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go环境，请确保Go已正确安装并配置环境变量
    pause
    exit /b 1
)

echo Go环境检查通过
echo.

REM 清理依赖
echo 正在清理和更新依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 依赖更新失败
    pause
    exit /b 1
)

echo 依赖更新完成
echo.

REM 复制图标文件
if exist "控制台程序\图标\gear_settings_options_icon_197126.ico" (
    echo 正在复制图标文件...
    copy "控制台程序\图标\gear_settings_options_icon_197126.ico" "icon.ico" >nul
)

REM 构建程序
echo 正在编译程序...
go build -ldflags "-s -w" -o bsphp-dnf.exe
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 构建成功！
echo 输出文件: bsphp-dnf.exe
echo.
echo 注意: 请以管理员身份运行程序以使用DNF变速器功能
echo.
pause

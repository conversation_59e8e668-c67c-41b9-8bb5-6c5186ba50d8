@echo off
chcp 65001 >nul
echo ================================
echo      安全编译脚本 v1.0
echo ================================
echo.

:: 检查Go环境
go version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Go环境，请先安装Go
    pause
    exit /b 1
)

echo [信息] 检测到Go环境正常

:: 设置编译参数
set BUILD_FLAGS=-ldflags="-s -w -H windowsgui"
set OUTPUT_NAME=bsphp_secure.exe

echo [信息] 开始安全编译...
echo [信息] 编译参数: %BUILD_FLAGS%
echo [信息] 输出文件: %OUTPUT_NAME%
echo.

:: 清理旧文件
if exist %OUTPUT_NAME% (
    echo [信息] 删除旧的可执行文件
    del %OUTPUT_NAME%
)

:: 检查是否安装了garble
garble version >nul 2>&1
if errorlevel 1 (
    echo [警告] 未安装garble代码混淆工具
    echo [信息] 正在安装garble...
    go install mvdan.cc/garble@latest
    if errorlevel 1 (
        echo [警告] garble安装失败，使用标准编译
        goto standard_build
    )
    echo [信息] garble安装成功
)

:: 使用garble混淆编译
echo [信息] 使用garble进行混淆编译...
garble build %BUILD_FLAGS% -o %OUTPUT_NAME%
if errorlevel 1 (
    echo [错误] garble编译失败，尝试标准编译
    goto standard_build
)
echo [信息] garble混淆编译成功
goto compress

:standard_build
echo [信息] 使用标准Go编译...
go build %BUILD_FLAGS% -o %OUTPUT_NAME%
if errorlevel 1 (
    echo [错误] 编译失败
    pause
    exit /b 1
)
echo [信息] 标准编译成功

:compress
:: 检查文件大小
for %%A in (%OUTPUT_NAME%) do set FILE_SIZE=%%~zA
echo [信息] 编译后文件大小: %FILE_SIZE% 字节

:: 检查是否安装了UPX
upx --version >nul 2>&1
if errorlevel 1 (
    echo [警告] 未安装UPX压缩工具，跳过压缩步骤
    echo [提示] 可从 https://upx.github.io/ 下载UPX
    goto finish
)

echo [信息] 使用UPX压缩可执行文件...
upx --best --lzma %OUTPUT_NAME%
if errorlevel 1 (
    echo [警告] UPX压缩失败，但编译成功
    goto finish
)

:: 显示压缩后大小
for %%A in (%OUTPUT_NAME%) do set COMPRESSED_SIZE=%%~zA
echo [信息] 压缩后文件大小: %COMPRESSED_SIZE% 字节

:finish
echo.
echo ================================
echo        编译完成！
echo ================================
echo [输出文件] %OUTPUT_NAME%
echo [安全特性] 
echo   ✓ 去除调试信息和符号表
echo   ✓ 隐藏控制台窗口
if not errorlevel 1 (
    echo   ✓ 代码混淆 (garble)
    echo   ✓ 文件压缩 (UPX)
)
echo.
echo [安全建议]
echo 1. 定期更新API密钥
echo 2. 监控异常登录行为  
echo 3. 考虑添加硬件绑定
echo 4. 实施服务器端验证
echo.

:: 询问是否运行程序
set /p RUN_PROGRAM="是否运行编译后的程序? (y/n): "
if /i "%RUN_PROGRAM%"=="y" (
    echo [信息] 启动程序...
    start %OUTPUT_NAME%
)

echo [完成] 按任意键退出...
pause >nul

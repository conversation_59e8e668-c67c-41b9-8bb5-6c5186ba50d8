package login

import (
	"bsphp/model"
	"bsphp/widgets"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
)

type EditPassForm struct {
	widget.BaseWidget
	data     model.EditPassData
	editPass func(model.EditPassData)
}

func NewEditPassForm() *EditPassForm {
	return &EditPassForm{}
}

func (u *EditPassForm) CreateRenderer() fyne.WidgetRenderer {
	u.data = model.EditPassData{
		Username:   binding.NewString(),
		Password:   binding.NewString(),
		NewPass:    binding.NewString(),
		RePassword: binding.NewString(),
		Code:       binding.NewString(),
	}
	usernameEntry := widget.NewEntryWithData(u.data.Username)
	usernameEntry.SetPlaceHolder("请输入用户名")
	passwordEntry := widgets.NewPassEntryWithData(u.data.Password)
	passwordEntry.SetPlaceHolder("请输入密码")
	newpassEntry := widgets.NewPassEntryWithData(u.data.NewPass)
	newpassEntry.SetPlaceHolder("请输入新密码")
	repasswordEntry := widgets.NewPassEntryWithData(u.data.RePassword)
	repasswordEntry.SetPlaceHolder("请再次新输入密码")

	form := container.NewVBox(
		usernameEntry,
		passwordEntry,
		newpassEntry,
		repasswordEntry,
		// codeEntry,
		widget.NewButton("修改密码", func() {
			u.editPass(u.data)
		}),
	)
	return widget.NewSimpleRenderer(container.NewPadded(form))
}

func (u *EditPassForm) SetOnEditPass(f func(model.EditPassData)) {
	u.editPass = f
}

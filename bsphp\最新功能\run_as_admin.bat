@echo off
echo 正在以管理员权限启动 BSphp DNF变速器...
echo.

REM 检查是否存在可执行文件
if not exist "bsphp-dnf.exe" (
    if not exist "bsphp.exe" (
        echo 错误: 未找到可执行文件
        echo 请先运行 build.bat 编译程序
        pause
        exit /b 1
    ) else (
        set EXECUTABLE=bsphp.exe
    )
) else (
    set EXECUTABLE=bsphp-dnf.exe
)

REM 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在请求管理员权限...
    powershell -Command "Start-Process '%~dp0%EXECUTABLE%' -Verb RunAs"
) else (
    echo 已具有管理员权限，直接启动程序...
    "%EXECUTABLE%"
)

echo.
pause

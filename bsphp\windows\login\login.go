package login

import (
	"bsphp/model"
	"bsphp/widgets"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"
)

type loginForm struct {
	widget.BaseWidget
	data         model.LoginData
	testNetwork  func()                // 测试网络函数
	checkVersion func()                // 检查版本函数
	login        func(model.LoginData) // 登录函数
	code         func() string         // 获取验证码函数
}

func NewLoginFrom() *loginForm {
	pe := &loginForm{
		data: model.LoginData{
			Username: binding.NewString(),
			Password: binding.NewString(),
			Code:     binding.NewString(),
		},
	}
	pe.ExtendBaseWidget(pe)
	return pe
}
func (pe *loginForm) CreateRenderer() fyne.WidgetRenderer {
	usernameEntry := widget.NewEntryWithData(pe.data.Username)
	usernameEntry.SetPlaceHolder("请输入用户名")
	passwordEntry := widgets.NewPassEntryWithData(pe.data.Password)
	passwordEntry.SetPlaceHolder("请输入密码")
	form := container.NewVBox(
		layout.NewSpacer(),
		usernameEntry,
		passwordEntry,
		container.NewGridWithColumns(
			3,
			widget.NewButton("测试网络", pe.testNetwork),
			widget.NewButton("检查版本", pe.checkVersion),
			widget.NewButton("账号登录", func() {
				pe.login(pe.data)
			}),
		),
		layout.NewSpacer(),
	)
	return widget.NewSimpleRenderer(container.NewPadded(form))
}
func (pe *loginForm) SetTestNetwork(testNetwork func()) {
	pe.testNetwork = testNetwork
}
func (pe *loginForm) SetCheckVersion(checkVersion func()) {
	pe.checkVersion = checkVersion
}
func (pe *loginForm) SetLogin(login func(data model.LoginData)) {
	pe.login = login
}

func (pe *loginForm) SetGetCode(code func() string) {
	pe.code = code
}

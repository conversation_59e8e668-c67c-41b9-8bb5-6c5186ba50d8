package main

import (
	"bufio"
	"bytes"
	"fmt"
	"os"
	"strconv"
	"strings"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

var (
	user32            = syscall.NewLazyDLL("user32.dll")
	procGetWindowText = user32.NewProc("GetWindowTextW")
)

const (
	TH32CS_SNAPPROCESS = 0x00000002
	TH32CS_SNAPMODULE  = 0x00000008
)

type PROCESSENTRY32 struct {
	dwSize              uint32
	cntUsage            uint32
	th32ProcessID       uint32
	th32DefaultHeapID   uintptr
	th32ModuleID        uint32
	cntThreads          uint32
	th32ParentProcessID uint32
	pcPriClassBase      int32
	dwFlags             uint32
	szExeFile           [260]uint16
}

type MODULEENTRY32 struct {
	dwSize        uint32
	th32ModuleID  uint32
	th32ProcessID uint32
	GlblcntUsage  uint32
	ProccntUsage  uint32
	modBaseAddr   uintptr
	modBaseSize   uint32
	hModule       windows.Handle
	szModule      [256]uint16
	szExePath     [260]uint16
}

// 获取用户输入的变速速率
func getUserInputSpeed() (int, error) {
	reader := bufio.NewReader(os.Stdin)

	for {
		fmt.Print("请输入变速速率(1-4)并按回车确认: ")
		input, err := reader.ReadString('\n')
		if err != nil {
			fmt.Println("输入错误，请重新输入")
			continue
		}

		input = strings.TrimSpace(input)
		speed, err := strconv.Atoi(input)
		if err != nil {
			fmt.Println("请输入有效的数字")
			continue
		}

		if speed > 4 {
			speed = 4
			fmt.Println("速率超过最大值，已自动设置为4")
		}

		if speed >= 1 && speed <= 4 {
			return speed, nil
		}

		fmt.Println("速率必须在1-4之间")
	}
}

// 查找所有进程 ID
func findAllProcessIDs(processName string) ([]uint32, error) {
	hSnapshot, err := windows.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0)
	if err != nil {
		return nil, fmt.Errorf("CreateToolhelp32Snapshot failed: %v", err)
	}
	defer windows.CloseHandle(hSnapshot)

	var pe32 PROCESSENTRY32
	pe32.dwSize = uint32(unsafe.Sizeof(pe32))

	if err := windows.Process32First(hSnapshot, (*windows.ProcessEntry32)(unsafe.Pointer(&pe32))); err != nil {
		return nil, fmt.Errorf("Process32First failed: %v", err)
	}

	var pids []uint32
	for {
		currentProcess := windows.UTF16ToString(pe32.szExeFile[:])
		if strings.EqualFold(currentProcess, processName) {
			pids = append(pids, pe32.th32ProcessID)
		}

		err := windows.Process32Next(hSnapshot, (*windows.ProcessEntry32)(unsafe.Pointer(&pe32)))
		if err != nil {
			if err == syscall.ERROR_NO_MORE_FILES {
				break
			}
			return nil, fmt.Errorf("Process32Next failed: %v", err)
		}
	}

	if len(pids) == 0 {
		return nil, fmt.Errorf("process %s not found", processName)
	}
	return pids, nil
}

// 查找模块基址
func findModuleBase(processID uint32, moduleName string) (uintptr, error) {
	hSnapshot, err := windows.CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, processID)
	if err != nil {
		return 0, fmt.Errorf("CreateToolhelp32Snapshot failed: %v", err)
	}
	defer windows.CloseHandle(hSnapshot)

	var me32 MODULEENTRY32
	me32.dwSize = uint32(unsafe.Sizeof(me32))

	if err := windows.Module32First(hSnapshot, (*windows.ModuleEntry32)(unsafe.Pointer(&me32))); err != nil {
		return 0, fmt.Errorf("Module32First failed: %v", err)
	}

	for {
		currentModule := windows.UTF16ToString(me32.szModule[:])
		if strings.EqualFold(currentModule, moduleName) {
			return me32.modBaseAddr, nil
		}

		err := windows.Module32Next(hSnapshot, (*windows.ModuleEntry32)(unsafe.Pointer(&me32)))
		if err != nil {
			if err == syscall.ERROR_NO_MORE_FILES {
				break
			}
			return 0, fmt.Errorf("Module32Next failed: %v", err)
		}
	}
	return 0, fmt.Errorf("module %s not found", moduleName)
}

func main() {
	// 先获取变速速率
	speed, err := getUserInputSpeed()
	if err != nil {
		fmt.Printf("获取速率失败: %v\n", err)
		fmt.Println("按回车键退出...")
		var input string
		fmt.Scanln(&input)
		return
	}

	fmt.Print("正在等待游戏1号进程启动")
	var modifiedPids = make(map[uint32]bool)

	// 第一阶段：等待并修改1号进程
	var pid1 uint32
	for dotCount := 0; ; {
		pids, err := findAllProcessIDs("DNF.exe")
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				if dotCount >= 10 {
					fmt.Print("\r正在等待游戏1号进程启动          \r正在等待游戏1号进程启动")
					dotCount = 0
				}
				dotCount++
				fmt.Print(".")
				time.Sleep(1 * time.Second)
				continue
			}
			fmt.Printf("查找进程失败: %v\n", err)
			fmt.Println("按回车键退出...")
			fmt.Scanln(new(string))
			return
		}

		// 获取第一个未修改的进程
		for _, pid := range pids {
			if !modifiedPids[pid] {
				pid1 = pid
				break
			}
		}

		if pid1 == 0 {
			continue
		}

		// 验证进程有效性
		valid := false
		for checkCount := 0; checkCount < 10; checkCount++ {
			time.Sleep(1 * time.Second)
			handle, err := windows.OpenProcess(windows.PROCESS_QUERY_INFORMATION, false, pid1)
			if err == nil {
				windows.CloseHandle(handle)
				valid = true
				break
			}
		}

		if valid {
			windowsHandles, err := getProcessWindows(pid1)
			if err == nil && len(windowsHandles) > 0 {
				break
			}
		}
	}

	fmt.Printf("\n已检测到游戏1号进程(PID: %d)\n", pid1)
	fmt.Printf("正在为1号进程设置速率为: %d\n", speed)
	_, err = modifyProcessMemory(pid1, speed)
	if err != nil {
		fmt.Printf("修改1号进程内存失败: %v\n", err)
	} else {
		fmt.Println("1号进程速率修改成功！")
		modifiedPids[pid1] = true
	}

	// 第二阶段：等待并修改2号进程
	fmt.Print("\n正在等待游戏2号进程启动")
	var pid2 uint32
	for dotCount := 0; ; {
		pids, err := findAllProcessIDs("DNF.exe")
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				if dotCount >= 10 {
					fmt.Print("\r正在等待游戏2号进程启动          \r正在等待游戏2号进程启动")
					dotCount = 0
				}
				dotCount++
				fmt.Print(".")
				time.Sleep(1 * time.Second)
				continue
			}
			fmt.Printf("查找进程失败: %v\n", err)
			fmt.Println("按回车键退出...")
			fmt.Scanln(new(string))
			return
		}

		// 获取第二个未修改的进程
		for _, pid := range pids {
			if !modifiedPids[pid] {
				pid2 = pid
				break
			}
		}

		if pid2 == 0 {
			continue
		}

		// 验证进程有效性
		valid := false
		for checkCount := 0; checkCount < 10; checkCount++ {
			time.Sleep(1 * time.Second)
			handle, err := windows.OpenProcess(windows.PROCESS_QUERY_INFORMATION, false, pid2)
			if err == nil {
				windows.CloseHandle(handle)
				valid = true
				break
			}
		}

		if valid {
			windowsHandles, err := getProcessWindows(pid2)
			if err == nil && len(windowsHandles) > 0 {
				break
			}
		}
	}

	fmt.Printf("\n已检测到游戏2号进程(PID: %d)\n", pid2)
	fmt.Printf("正在为2号进程设置速率为: %d\n", speed)
	_, err = modifyProcessMemory(pid2, speed)
	if err != nil {
		fmt.Printf("修改2号进程内存失败: %v\n", err)
	} else {
		fmt.Println("2号进程速率修改成功！")
		modifiedPids[pid2] = true
	}

	fmt.Println("速率修改成功！按回车键退出...")
	var input string
	fmt.Scanln(&input)
}

func getProcessWindows(pid uint32) ([]windows.Handle, error) {
	var handles []windows.Handle
	callback := func(hwnd windows.Handle, lParam uintptr) bool {
		var pidFromWindow uint32
		windows.GetWindowThreadProcessId(windows.HWND(hwnd), &pidFromWindow)
		if pidFromWindow == pid {
			handles = append(handles, hwnd)
		}
		return true
	}

	err := windows.EnumWindows(syscall.NewCallback(func(hwnd windows.Handle, lParam uintptr) uintptr {
		if callback(hwnd, lParam) {
			return 1
		}
		return 0
	}), unsafe.Pointer(nil))
	return handles, err
}

func getWindowClassName(hwnd windows.Handle) (string, error) {
	var className [256]uint16
	_, err := windows.GetClassName(windows.HWND(hwnd), &className[0], int32(len(className)))
	if err != nil {
		return "", err
	}
	return windows.UTF16ToString(className[:]), nil
}

func getWindowTitle(hwnd windows.Handle) (string, error) {
	var title [256]uint16
	ret, _, err := procGetWindowText.Call(
		uintptr(hwnd),
		uintptr(unsafe.Pointer(&title[0])),
		uintptr(len(title)),
	)
	if ret == 0 {
		return "", err
	}
	return windows.UTF16ToString(title[:ret]), nil
}

func modifyProcessMemory(pid uint32, speed int) (uintptr, error) {
	processHandle, err := windows.OpenProcess(
		windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ|windows.PROCESS_VM_WRITE|windows.PROCESS_VM_OPERATION,
		false,
		pid,
	)
	if err != nil {
		return 0, fmt.Errorf("打开进程失败: %v", err)
	}
	defer windows.CloseHandle(processHandle)

	kernel32Base, err := findModuleBase(pid, "KERNEL32.DLL")
	if err != nil {
		return 0, fmt.Errorf("查找KERNEL32.DLL失败: %v", err)
	}

	proc, err := windows.GetProcAddress(windows.Handle(kernel32Base), "timeGetTime")
	if err != nil {
		return 0, fmt.Errorf("获取timeGetTime地址失败: %v", err)
	}

	targetSig := []byte{0x48, 0xC1, 0xFA, 0x0B}
	searchSize := 100
	buffer := make([]byte, searchSize)

	var bytesRead uintptr
	err = windows.ReadProcessMemory(
		processHandle,
		proc,
		&buffer[0],
		uintptr(searchSize),
		&bytesRead,
	)
	if err != nil {
		return 0, fmt.Errorf("读取内存失败: %v", err)
	}
	if bytesRead < uintptr(len(targetSig)) {
		return 0, fmt.Errorf("读取的内存不足，无法搜索特征码")
	}

	var foundOffset = -1
	actualRead := int(bytesRead)
	maxIndex := actualRead - len(targetSig)
	for i := 0; i <= maxIndex; i++ {
		if bytes.Equal(buffer[i:i+len(targetSig)], targetSig) {
			foundOffset = i
			break
		}
	}

	if foundOffset == -1 {
		return 0, fmt.Errorf("未找到目标特征码（48 C1 FA 0B）")
	}

	targetAddr := proc + uintptr(foundOffset)
	var newSig []byte
	switch {
	case speed == 1:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x0A}
	case speed == 2:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x09}
	case speed == 3:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x08}
	case speed == 4:
		newSig = []byte{0x48, 0xC1, 0xFA, 0x07}
	default:
		return 0, fmt.Errorf("无效速率值: %d", speed)
	}

	var bytesWritten uintptr
	err = windows.WriteProcessMemory(
		processHandle,
		targetAddr,
		&newSig[0],
		uintptr(len(newSig)),
		&bytesWritten,
	)
	if err != nil {
		return 0, fmt.Errorf("内存写入失败: %v", err)
	}
	if bytesWritten != uintptr(len(newSig)) {
		return 0, fmt.Errorf("内存写入不完整，仅写入%d字节", bytesWritten)
	}

	return targetAddr, nil
}

// 查找第一个匹配名称的进程PID
func findProcessID(processName string) (uint32, error) {
	pids, err := findAllProcessIDs(processName)
	if err != nil {
		return 0, err
	}
	if len(pids) == 0 {
		return 0, fmt.Errorf("no process found with name %s", processName)
	}
	// 返回第一个找到的进程ID
	return pids[0], nil
}

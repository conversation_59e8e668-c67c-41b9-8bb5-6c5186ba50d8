@echo off
echo 正在修复依赖并构建程序...
echo.

REM 设置Go代理（如果网络有问题）
set GOPROXY=https://goproxy.cn,direct
set GOSUMDB=sum.golang.google.cn

echo 尝试方法1: 清理模块缓存并重新下载
go clean -modcache
go mod download
if %errorlevel% equ 0 (
    echo 依赖下载成功，开始编译...
    go build -o bsphp-dnf.exe
    if %errorlevel% equ 0 (
        echo 编译成功！
        goto :success
    )
)

echo.
echo 尝试方法2: 使用go mod tidy修复依赖
go mod tidy
if %errorlevel% equ 0 (
    echo go mod tidy成功，开始编译...
    go build -o bsphp-dnf.exe
    if %errorlevel% equ 0 (
        echo 编译成功！
        goto :success
    )
)

echo.
echo 尝试方法3: 强制更新特定依赖
go get golang.org/x/sys@latest
go get fyne.io/fyne/v2@v2.6.1
if %errorlevel% equ 0 (
    echo 依赖更新成功，开始编译...
    go build -o bsphp-dnf.exe
    if %errorlevel% equ 0 (
        echo 编译成功！
        goto :success
    )
)

echo.
echo 尝试方法4: 离线编译（忽略校验和）
set GOSUMDB=off
go build -o bsphp-dnf.exe
if %errorlevel% equ 0 (
    echo 编译成功！
    goto :success
)

echo.
echo 所有方法都失败了。可能的原因：
echo 1. 网络连接问题
echo 2. Go环境配置问题
echo 3. 防火墙阻止
echo.
echo 建议：
echo 1. 检查网络连接
echo 2. 尝试使用VPN
echo 3. 配置Go代理: go env -w GOPROXY=https://goproxy.cn,direct
echo.
goto :end

:success
echo.
echo 构建成功！输出文件: bsphp-dnf.exe
echo 请以管理员身份运行程序以使用DNF变速器功能
echo.

:end
pause

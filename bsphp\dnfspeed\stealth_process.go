package dnfspeed

import (
	"fmt"
	"syscall"
	"unsafe"

	"golang.org/x/sys/windows"
)

// StealthProcess 隐蔽进程操作器
type StealthProcess struct {
	ntdll    *windows.LazyDLL
	kernel32 *windows.LazyDLL
}

// NewStealthProcess 创建隐蔽进程操作器
func NewStealthProcess() *StealthProcess {
	return &StealthProcess{
		ntdll:    windows.NewLazySystemDLL("ntdll.dll"),
		kernel32: windows.NewLazySystemDLL("kernel32.dll"),
	}
}

// OpenProcessNt 使用 NtOpenProcess 替代 OpenProcess
func (sp *StealthProcess) OpenProcessNt(pid uint32, desiredAccess uint32) (windows.Handle, error) {
	ntOpenProcess := sp.ntdll.NewProc("NtOpenProcess")

	var handle windows.Handle
	var clientId struct {
		UniqueProcess uintptr
		UniqueThread  uintptr
	}
	clientId.UniqueProcess = uintptr(pid)

	var objectAttributes struct {
		Length                   uint32
		RootDirectory            windows.Handle
		ObjectName               uintptr
		Attributes               uint32
		SecurityDescriptor       uintptr
		SecurityQualityOfService uintptr
	}
	objectAttributes.Length = uint32(unsafe.Sizeof(objectAttributes))

	ret, _, _ := ntOpenProcess.Call(
		uintptr(unsafe.Pointer(&handle)),
		uintptr(desiredAccess),
		uintptr(unsafe.Pointer(&objectAttributes)),
		uintptr(unsafe.Pointer(&clientId)),
	)

	if ret != 0 {
		return 0, fmt.Errorf("NtOpenProcess failed with NTSTATUS: 0x%x", ret)
	}

	return handle, nil
}

// OpenProcessViaDebug 通过调试权限打开进程
func (sp *StealthProcess) OpenProcessViaDebug(pid uint32) (windows.Handle, error) {
	// 首先获取调试权限
	err := sp.enableDebugPrivilege()
	if err != nil {
		return 0, fmt.Errorf("enable debug privilege failed: %v", err)
	}

	// 使用 NtOpenProcess 打开进程
	return sp.OpenProcessNt(pid, 0x1FFFFF) // PROCESS_ALL_ACCESS
}

// enableDebugPrivilege 启用调试权限
func (sp *StealthProcess) enableDebugPrivilege() error {
	var token windows.Token
	err := windows.OpenProcessToken(windows.CurrentProcess(), windows.TOKEN_ADJUST_PRIVILEGES|windows.TOKEN_QUERY, &token)
	if err != nil {
		return err
	}
	defer token.Close()

	var luid windows.LUID
	err = windows.LookupPrivilegeValue(nil, windows.StringToUTF16Ptr("SeDebugPrivilege"), &luid)
	if err != nil {
		return err
	}

	privileges := windows.Tokenprivileges{
		PrivilegeCount: 1,
		Privileges: [1]windows.LUIDAndAttributes{
			{
				Luid:       luid,
				Attributes: windows.SE_PRIVILEGE_ENABLED,
			},
		},
	}

	return windows.AdjustTokenPrivileges(token, false, &privileges, 0, nil, nil)
}

// ReadProcessMemoryNt 使用 NtReadVirtualMemory 替代 ReadProcessMemory
func (sp *StealthProcess) ReadProcessMemoryNt(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	ntReadVirtualMemory := sp.ntdll.NewProc("NtReadVirtualMemory")

	buffer := make([]byte, size)
	var bytesRead uintptr

	ret, _, _ := ntReadVirtualMemory.Call(
		uintptr(processHandle),
		address,
		uintptr(unsafe.Pointer(&buffer[0])),
		size,
		uintptr(unsafe.Pointer(&bytesRead)),
	)

	if ret != 0 {
		return nil, fmt.Errorf("NtReadVirtualMemory failed with NTSTATUS: 0x%x", ret)
	}

	if bytesRead < size {
		return buffer[:bytesRead], nil
	}

	return buffer, nil
}

// ReadProcessMemoryViaSharedMemory 通过共享内存读取
func (sp *StealthProcess) ReadProcessMemoryViaSharedMemory(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	// 创建文件映射
	mappingHandle, err := windows.CreateFileMapping(
		windows.InvalidHandle,
		nil,
		windows.PAGE_READWRITE,
		0,
		uint32(size),
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("CreateFileMapping failed: %v", err)
	}
	defer windows.CloseHandle(mappingHandle)

	// 在当前进程中映射
	localView, err := windows.MapViewOfFile(
		mappingHandle,
		0x0006, // FILE_MAP_READ | FILE_MAP_WRITE
		0,
		0,
		size,
	)
	if err != nil {
		return nil, fmt.Errorf("MapViewOfFile failed: %v", err)
	}
	defer windows.UnmapViewOfFile(localView)

	// 在目标进程中映射到相同地址
	err = sp.mapViewInTargetProcess(processHandle, mappingHandle, address, size)
	if err != nil {
		return nil, fmt.Errorf("map in target process failed: %v", err)
	}

	// 从本地视图读取数据
	buffer := make([]byte, size)
	copy(buffer, (*[1 << 30]byte)(unsafe.Pointer(localView))[:size])

	return buffer, nil
}

// mapViewInTargetProcess 在目标进程中映射视图
func (sp *StealthProcess) mapViewInTargetProcess(processHandle windows.Handle, mappingHandle windows.Handle, address uintptr, size uintptr) error {
	ntMapViewOfSection := sp.ntdll.NewProc("NtMapViewOfSection")

	baseAddress := address
	viewSize := size

	ret, _, _ := ntMapViewOfSection.Call(
		uintptr(mappingHandle),
		uintptr(processHandle),
		uintptr(unsafe.Pointer(&baseAddress)),
		0,
		0,
		0,
		uintptr(unsafe.Pointer(&viewSize)),
		1, // ViewUnmap
		0,
		windows.PAGE_READWRITE,
	)

	if ret != 0 {
		return fmt.Errorf("NtMapViewOfSection failed with NTSTATUS: 0x%x", ret)
	}

	return nil
}

// ReadProcessMemoryViaInjection 通过代码注入读取内存（简化版本）
func (sp *StealthProcess) ReadProcessMemoryViaInjection(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	// 简化实现：直接返回错误，让它回退到其他方法
	return nil, fmt.Errorf("injection method not implemented in simplified version")
}

// GetProcessHandleViaSnapshot 通过快照获取进程句柄
func (sp *StealthProcess) GetProcessHandleViaSnapshot(pid uint32) (windows.Handle, error) {
	// 创建进程快照
	snapshot, err := windows.CreateToolhelp32Snapshot(windows.TH32CS_SNAPPROCESS, 0)
	if err != nil {
		return 0, fmt.Errorf("CreateToolhelp32Snapshot failed: %v", err)
	}
	defer windows.CloseHandle(snapshot)

	// 遍历进程找到目标PID
	var pe32 windows.ProcessEntry32
	pe32.Size = uint32(unsafe.Sizeof(pe32))

	err = windows.Process32First(snapshot, &pe32)
	if err != nil {
		return 0, fmt.Errorf("Process32First failed: %v", err)
	}

	for {
		if pe32.ProcessID == pid {
			// 找到目标进程，使用NtOpenProcess打开
			return sp.OpenProcessNt(pid, windows.PROCESS_ALL_ACCESS)
		}

		err = windows.Process32Next(snapshot, &pe32)
		if err != nil {
			if err == syscall.ERROR_NO_MORE_FILES {
				break
			}
			return 0, fmt.Errorf("Process32Next failed: %v", err)
		}
	}

	return 0, fmt.Errorf("process with PID %d not found", pid)
}

// ReadMemoryWithFallback 带备用方案的内存读取
func (sp *StealthProcess) ReadMemoryWithFallback(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	// 方法1: 手动系统调用
	manualSyscall := NewManualSyscall()
	data, err := manualSyscall.DirectSyscallReadMemory(processHandle, address, size)
	if err == nil {
		return data, nil
	}

	// 方法2: NtReadVirtualMemory
	data, err = sp.ReadProcessMemoryNt(processHandle, address, size)
	if err == nil {
		return data, nil
	}

	// 方法3: 共享内存
	data, err = sp.ReadProcessMemoryViaSharedMemory(processHandle, address, size)
	if err == nil {
		return data, nil
	}

	// 方法4: 代码注入
	data, err = sp.ReadProcessMemoryViaInjection(processHandle, address, size)
	if err == nil {
		return data, nil
	}

	// 方法5: 标准方法（最后备用）
	buffer := make([]byte, size)
	var bytesRead uintptr
	err = windows.ReadProcessMemory(
		processHandle,
		address,
		&buffer[0],
		size,
		&bytesRead,
	)
	if err != nil {
		return nil, fmt.Errorf("all read methods failed, last error: %v", err)
	}

	return buffer[:bytesRead], nil
}

// OpenProcessWithFallback 带备用方案的进程打开
func (sp *StealthProcess) OpenProcessWithFallback(pid uint32, desiredAccess uint32) (windows.Handle, error) {
	// 方法1: 手动系统调用
	manualSyscall := NewManualSyscall()
	handle, err := manualSyscall.DirectSyscallOpenProcess(pid)
	if err == nil {
		return handle, nil
	}

	// 方法2: NtOpenProcess
	handle, err = sp.OpenProcessNt(pid, desiredAccess)
	if err == nil {
		return handle, nil
	}

	// 方法3: 通过调试权限
	handle, err = sp.OpenProcessViaDebug(pid)
	if err == nil {
		return handle, nil
	}

	// 方法4: 通过快照
	handle, err = sp.GetProcessHandleViaSnapshot(pid)
	if err == nil {
		return handle, nil
	}

	// 方法5: 标准方法（最后备用）
	handle, err = windows.OpenProcess(desiredAccess, false, pid)
	if err != nil {
		return 0, fmt.Errorf("all open methods failed, last error: %v", err)
	}

	return handle, nil
}

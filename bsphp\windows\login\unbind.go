package login

import (
	"bsphp/model"
	"bsphp/widgets"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
)

type UnbindForm struct {
	widget.BaseWidget
	data   model.UnbindData
	unbind func(model.UnbindData)
}

func NewUnbindForm() *UnbindForm {
	return &UnbindForm{}
}

func (u *UnbindForm) CreateRenderer() fyne.WidgetRenderer {
	u.data = model.UnbindData{
		Username: binding.NewString(),
		Password: binding.NewString(),
	}
	usernameEntry := widget.NewEntryWithData(u.data.Username)
	usernameEntry.SetPlaceHolder("请输入用户名")
	passwordEntry := widgets.NewPassEntryWithData(u.data.Password)
	passwordEntry.SetPlaceHolder("请输入密码")

	form := container.NewVBox(
		usernameEntry,
		passwordEntry,
		widget.NewButton("解除绑定", func() {
			u.unbind(u.data)
		}),
	)
	return widget.NewSimpleRenderer(container.NewPadded(form))
}

func (u *UnbindForm) SetOnUnbind(unbind func(model.UnbindData)) {
	u.unbind = unbind
}

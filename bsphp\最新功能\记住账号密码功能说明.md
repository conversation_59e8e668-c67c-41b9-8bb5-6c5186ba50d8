# 记住账号密码功能说明

## 功能概述

为登录界面添加了"记住账号密码"功能，用户可以选择保存登录凭据，下次打开程序时自动填充账号和密码。

## 功能特点

### 🔐 **安全性**
- **AES加密存储** - 使用AES-256加密算法保护存储的密码
- **机器绑定** - 基于机器特征生成加密密钥，其他机器无法解密
- **自动过期** - 保存的凭据30天后自动过期
- **安全删除** - 取消记住密码时自动清除保存的文件

### 💾 **存储方式**
- **本地存储** - 凭据保存在程序目录下的`config.dat`文件中
- **加密保护** - 文件内容经过加密，无法直接读取
- **权限控制** - 文件权限设置为仅当前用户可读写(0600)

### 🎯 **用户体验**
- **一键记住** - 勾选"记住账号密码"复选框即可
- **自动填充** - 下次启动时自动填充保存的账号密码
- **智能清除** - 取消勾选时自动清除已保存的凭据

## 实现细节

### 1. 核心文件

#### `config/credentials.go`
- **CredentialsManager** - 凭据管理器主类
- **Credentials** - 凭据数据结构
- **加密/解密方法** - AES加密实现
- **文件操作** - 安全的文件读写

#### `model/login.go`
- 添加了 `RememberMe binding.Bool` 字段

#### `windows/login/login.go`
- 添加了"记住账号密码"复选框
- 添加了 `SetLoginData()` 方法用于自动填充

#### `windows/login.go`
- 集成了凭据管理器
- 实现了保存和加载逻辑

### 2. 数据结构

```go
type Credentials struct {
    Username   string `json:"username"`    // 用户名
    Password   string `json:"password"`    // 密码（加密存储）
    RememberMe bool   `json:"remember_me"` // 是否记住密码
    LastLogin  int64  `json:"last_login"`  // 最后登录时间戳
}
```

### 3. 加密机制

#### 密钥生成
```go
// 基于机器特征生成密钥
machineInfo := hostname + "_bsphp_app_key"
encryptKey := sha256(machineInfo)
```

#### 加密流程
1. 生成随机IV（初始化向量）
2. 使用AES-CFB模式加密数据
3. 组合IV和密文
4. Base64编码存储

#### 解密流程
1. Base64解码
2. 分离IV和密文
3. 使用相同密钥解密
4. 验证数据完整性

### 4. 安全特性

#### 防止数据泄露
- 密码经过AES加密，即使文件被获取也无法直接读取
- 加密密钥基于机器特征，其他机器无法解密
- 文件权限限制为当前用户

#### 防止重放攻击
- 凭据包含时间戳，30天后自动过期
- 每次登录成功后更新时间戳

#### 错误处理
- 解密失败时自动删除损坏的配置文件
- 加载失败时不影响正常登录流程

## 使用流程

### 1. 首次使用
1. 用户在登录界面输入账号密码
2. 勾选"记住账号密码"复选框
3. 点击"账号登录"按钮
4. 登录成功后，凭据被加密保存到本地

### 2. 后续使用
1. 程序启动时自动加载保存的凭据
2. 如果凭据有效，自动填充登录表单
3. 用户可以直接点击登录，或修改后登录

### 3. 取消记住
1. 取消勾选"记住账号密码"复选框
2. 登录时会自动清除已保存的凭据
3. 下次启动时不会自动填充

## 文件位置

### 配置文件
- **路径**: `程序目录/config.dat`
- **格式**: 加密的JSON数据
- **权限**: 仅当前用户可读写

### 示例文件内容（加密前）
```json
{
    "username": "testuser",
    "password": "testpass",
    "remember_me": true,
    "last_login": 1703123456
}
```

## 安全建议

### 1. 用户建议
- 仅在个人电脑上使用记住密码功能
- 定期更改密码以提高安全性
- 在共享电脑上不要使用此功能

### 2. 开发建议
- 定期更新加密算法
- 考虑添加主密码功能
- 监控异常的文件访问

## 故障排除

### 1. 自动填充不工作
- 检查`config.dat`文件是否存在
- 确认文件权限是否正确
- 检查凭据是否过期（30天）

### 2. 保存失败
- 确认程序目录有写入权限
- 检查磁盘空间是否充足
- 确认没有安全软件阻止文件写入

### 3. 解密失败
- 可能是文件损坏，程序会自动删除并重新开始
- 可能是在不同机器上使用，需要重新保存凭据

## 技术优势

### 1. 安全性
- 使用工业级AES加密算法
- 机器绑定防止凭据被盗用
- 自动过期机制防止长期风险

### 2. 兼容性
- 跨平台支持（Windows/Linux/macOS）
- 不依赖外部库或服务
- 向后兼容，不影响现有功能

### 3. 性能
- 加密/解密操作快速
- 文件体积小（通常<1KB）
- 启动时加载速度快

## 未来改进

### 1. 短期计划
- 添加密码强度验证
- 支持多账号保存
- 添加导入/导出功能

### 2. 长期计划
- 集成硬件安全模块
- 支持生物识别验证
- 云端同步功能

## 总结

记住账号密码功能为用户提供了便利的同时，通过多层安全机制确保了凭据的安全性。该功能设计简洁、实现可靠，能够满足大多数用户的需求。

### 主要优点
- ✅ 安全的加密存储
- ✅ 简单易用的界面
- ✅ 自动过期保护
- ✅ 跨平台兼容
- ✅ 不影响现有功能

### 使用建议
- 在个人设备上使用以获得最佳安全性
- 定期更新密码以维护账户安全
- 如有安全疑虑可随时取消记住功能

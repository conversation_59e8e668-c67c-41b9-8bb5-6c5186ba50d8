package security

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 加密配置结构
type EncryptedConfig struct {
	URL         string `json:"url"`
	Key         string `json:"key"`
	MutualKey   string `json:"mutual_key"`
	SendSignKey string `json:"send_sign_key"`
	RecSignKey  string `json:"rec_sign_key"`
	Timestamp   int64  `json:"timestamp"`
	Checksum    string `json:"checksum"`
}

// 配置管理器
type ConfigManager struct {
	serverURL string
	localKey  []byte
}

// NewConfigManager 创建配置管理器
func NewConfigManager(serverURL string) *ConfigManager {
	// 生成本地密钥（基于硬件信息）
	localKey := generateLocalKey()
	
	return &ConfigManager{
		serverURL: serverURL,
		localKey:  localKey,
	}
}

// 从服务器获取加密配置
func (cm *ConfigManager) FetchConfig() (*EncryptedConfig, error) {
	// 构建请求
	req, err := http.NewRequest("GET", cm.serverURL+"/config", nil)
	if err != nil {
		return nil, err
	}
	
	// 添加客户端标识
	req.Header.Set("Client-ID", cm.generateClientID())
	req.Header.Set("User-Agent", "SecureClient/1.0")
	
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("服务器返回错误: %d", resp.StatusCode)
	}
	
	// 读取加密数据
	encryptedData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	// 解密配置
	configData, err := cm.decryptConfig(encryptedData)
	if err != nil {
		return nil, err
	}
	
	var config EncryptedConfig
	err = json.Unmarshal(configData, &config)
	if err != nil {
		return nil, err
	}
	
	// 验证配置完整性
	if !cm.validateConfig(&config) {
		return nil, fmt.Errorf("配置验证失败")
	}
	
	return &config, nil
}

// 生成客户端ID
func (cm *ConfigManager) generateClientID() string {
	// 基于硬件信息生成唯一ID
	hash := sha256.Sum256(cm.localKey)
	return base64.StdEncoding.EncodeToString(hash[:16])
}

// 解密配置
func (cm *ConfigManager) decryptConfig(encryptedData []byte) ([]byte, error) {
	// 解码base64
	ciphertext, err := base64.StdEncoding.DecodeString(string(encryptedData))
	if err != nil {
		return nil, err
	}
	
	// 创建AES解密器
	block, err := aes.NewCipher(cm.localKey[:32])
	if err != nil {
		return nil, err
	}
	
	// 提取IV
	if len(ciphertext) < aes.BlockSize {
		return nil, fmt.Errorf("密文太短")
	}
	
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]
	
	// 解密
	stream := cipher.NewCFBDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	stream.XORKeyStream(plaintext, ciphertext)
	
	return plaintext, nil
}

// 验证配置
func (cm *ConfigManager) validateConfig(config *EncryptedConfig) bool {
	// 检查时间戳（防止重放攻击）
	now := time.Now().Unix()
	if now-config.Timestamp > 3600 { // 1小时有效期
		return false
	}
	
	// 验证校验和
	data := fmt.Sprintf("%s%s%s%d", 
		config.URL, config.Key, config.MutualKey, config.Timestamp)
	hash := sha256.Sum256([]byte(data))
	expectedChecksum := base64.StdEncoding.EncodeToString(hash[:])
	
	return config.Checksum == expectedChecksum
}

// 生成本地密钥
func generateLocalKey() []byte {
	// 这里应该基于硬件信息生成密钥
	// 比如CPU ID、MAC地址、硬盘序列号等
	// 为了演示，这里使用固定值
	key := make([]byte, 32)
	copy(key, []byte("your-hardware-based-key-here"))
	return key
}

// 加密存储配置
func (cm *ConfigManager) EncryptAndStore(config *EncryptedConfig, filePath string) error {
	// 序列化配置
	data, err := json.Marshal(config)
	if err != nil {
		return err
	}
	
	// 加密数据
	encryptedData, err := cm.encryptData(data)
	if err != nil {
		return err
	}
	
	// 写入文件（这里可以进一步混淆文件格式）
	return writeToFile(filePath, encryptedData)
}

// 加密数据
func (cm *ConfigManager) encryptData(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(cm.localKey[:32])
	if err != nil {
		return nil, err
	}
	
	// 生成随机IV
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}
	
	// 加密
	stream := cipher.NewCFBEncrypter(block, iv)
	ciphertext := make([]byte, len(data))
	stream.XORKeyStream(ciphertext, data)
	
	// 组合IV和密文
	result := append(iv, ciphertext...)
	
	// 编码为base64
	encoded := base64.StdEncoding.EncodeToString(result)
	return []byte(encoded), nil
}

// 写入文件的占位函数
func writeToFile(filePath string, data []byte) error {
	// 实际实现中应该写入文件
	return nil
}

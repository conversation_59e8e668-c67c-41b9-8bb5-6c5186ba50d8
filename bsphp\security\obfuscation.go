package security

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"runtime"
	"time"
)

// 动态密钥生成器
type KeyGenerator struct {
	seed []byte
	salt []byte
}

// NewKeyGenerator 创建新的密钥生成器
func NewKeyGenerator() *KeyGenerator {
	seed := make([]byte, 32)
	salt := make([]byte, 16)
	rand.Read(seed)
	rand.Read(salt)

	return &KeyGenerator{
		seed: seed,
		salt: salt,
	}
}

// GenerateKey 基于时间和系统信息生成动态密钥
func (kg *KeyGenerator) GenerateKey() string {
	// 获取系统信息
	sysInfo := fmt.Sprintf("%s_%s_%d",
		runtime.GOOS,
		runtime.GOARCH,
		time.Now().Unix()/3600) // 每小时变化一次

	// 组合种子
	combined := append(kg.seed, []byte(sysInfo)...)
	combined = append(combined, kg.salt...)

	// 生成哈希
	hash := sha256.Sum256(combined)
	return hex.EncodeToString(hash[:16])
}

// 反调试检测
func AntiDebug() bool {
	// 检测调试器
	if isDebuggerPresent() {
		return false
	}

	// 检测虚拟机
	if isVirtualMachine() {
		return false
	}

	// 时间检测
	if timeCheck() {
		return false
	}

	return true
}

// 检测调试器
func isDebuggerPresent() bool {
	// 简单的调试器检测
	start := time.Now()
	time.Sleep(time.Millisecond)
	elapsed := time.Since(start)

	// 如果执行时间异常长，可能被调试
	return elapsed > time.Millisecond*10
}

// 检测虚拟机
func isVirtualMachine() bool {
	// 检测常见虚拟机特征
	vmIndicators := []string{
		"VMware", "VirtualBox", "QEMU", "Xen", "Hyper-V",
	}

	// 这里可以添加更复杂的虚拟机检测逻辑
	// 比如检查MAC地址、硬件信息等
	_ = vmIndicators
	return false
}

// 时间检测
func timeCheck() bool {
	start := time.Now()
	// 执行一些计算
	sum := 0
	for i := 0; i < 1000; i++ {
		sum += i
	}
	elapsed := time.Since(start)

	// 如果执行时间异常，可能被调试或分析
	return elapsed > time.Millisecond*100
}

// 字符串混淆
func ObfuscateString(s string) string {
	encoded := base64.StdEncoding.EncodeToString([]byte(s))
	return encoded
}

// 字符串反混淆
func DeobfuscateString(s string) string {
	decoded, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		return ""
	}
	return string(decoded)
}

// 程序完整性检查
func ProgramIntegrityCheck() bool {
	// 检查程序文件的完整性
	// 可以预先计算程序的哈希值，运行时验证
	return true
}

// 网络时间验证
func NetworkTimeValidation() bool {
	// 从可信的时间服务器获取时间
	// 防止系统时间被修改
	return true
}
